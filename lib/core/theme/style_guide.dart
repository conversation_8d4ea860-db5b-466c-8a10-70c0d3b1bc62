import 'package:flutter/material.dart';
import 'colors.dart';

/// OneApp UI Style Guide
/// A centralized collection of styles, measurements, and guidelines
/// to ensure consistency across all screens in the OneApp application.
class AppStyleGuide {
  // SPACING
  static const double spacingXS = 4.0;
  static const double spacingS = 8.0;
  static const double spacingM = 16.0;
  static const double spacingL = 24.0;
  static const double spacingXL = 32.0;
  static const double spacingXXL = 48.0;

  // PADDING
  static const EdgeInsets paddingAll = EdgeInsets.all(spacingM);
  static const EdgeInsets paddingHorizontal = EdgeInsets.symmetric(
    horizontal: spacingM,
  );
  static const EdgeInsets paddingVertical = EdgeInsets.symmetric(
    vertical: spacingM,
  );
  static const EdgeInsets paddingPage = EdgeInsets.all(spacingM);

  // BORDER RADIUS
  static const double borderRadiusS = 8.0;
  static const double borderRadiusM = 12.0;
  static const double borderRadiusL = 16.0;
  static const double borderRadiusXL = 24.0;
  static const double borderRadiusCircular = 50.0;

  // ELEVATION
  static const double elevationNone = 0.0;
  static const double elevationXS = 1.0;
  static const double elevationS = 2.0;
  static const double elevationM = 3.0;
  static const double elevationL = 4.0;
  static const double elevationXL = 8.0;

  // TYPOGRAPHY
  static TextStyle get headingLarge => const TextStyle(
        fontSize: 24.0,
        fontWeight: FontWeight.bold,
        color: AppColors.textPrimary,
      );

  static TextStyle get headingMedium => const TextStyle(
        fontSize: 20.0,
        fontWeight: FontWeight.bold,
        color: AppColors.textPrimary,
      );

  static TextStyle get headingSmall => const TextStyle(
        fontSize: 20.0,
        fontWeight: FontWeight.bold,
        color: AppColors.textPrimary,
      );

  static TextStyle get subtitle => const TextStyle(
        fontSize: 16.0,
        fontWeight: FontWeight.w500,
        color: AppColors.textSecondary,
      );

  static TextStyle get bodyLarge =>
      const TextStyle(fontSize: 16.0, color: AppColors.textSecondary);

  static TextStyle get bodyMedium =>
      const TextStyle(fontSize: 14.0, color: AppColors.textSecondary);

  static TextStyle get bodySmall =>
      const TextStyle(fontSize: 12.0, color: AppColors.textLight);

  static TextStyle get buttonText => const TextStyle(
        fontSize: 14.0,
        fontWeight: FontWeight.bold,
        color: Colors.white,
      );

  // SHADOWS
  static List<BoxShadow> get shadowLight => [
        BoxShadow(
          color: Colors.black.withOpacity(0.05),
          blurRadius: 8,
          offset: const Offset(0, 2),
        ),
      ];

  static List<BoxShadow> get shadowMedium => [
        BoxShadow(
          color: Colors.black.withOpacity(0.08),
          blurRadius: 12,
          offset: const Offset(0, 4),
        ),
      ];

  static List<BoxShadow> get shadowHeavy => [
        BoxShadow(
          color: Colors.black.withOpacity(0.12),
          blurRadius: 16,
          offset: const Offset(0, 6),
        ),
      ];

  // APP BAR
  static AppBar standardAppBar({
    required String title,
    List<Widget>? actions,
    bool centerTitle = false,
    bool useGradient = false,
    PreferredSizeWidget? bottom,
  }) {
    return AppBar(
      elevation: 0,
      centerTitle: centerTitle,
      title: Text(
        title,
        style: TextStyle(
          color: useGradient ? Colors.white : AppColors.textPrimary,
          fontWeight: FontWeight.bold,
          fontSize: 18,
        ),
        textAlign: TextAlign.left,
      ),
      flexibleSpace: useGradient
          ? Container(
              decoration: const BoxDecoration(
                gradient: LinearGradient(
                  colors: [AppColors.primary, Color(0xFF931313)],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
              ),
            )
          : null,
      backgroundColor: useGradient ? null : Colors.white,
      iconTheme: IconThemeData(
        color: useGradient ? Colors.white : AppColors.textPrimary,
      ),
      actions: actions,
      bottom: bottom,
    );
  }

  // TAB BAR
  static TabBar standardTabBar({
    required TabController controller,
    required List<Widget> tabs,
  }) {
    return TabBar(
      controller: controller,
      labelColor: AppColors.primary,
      unselectedLabelColor: AppColors.textLight,
      indicatorColor: AppColors.primary,
      indicatorWeight: 3,
      indicatorSize: TabBarIndicatorSize.tab,
      labelPadding: const EdgeInsets.symmetric(horizontal: 4, vertical: 12),
      labelStyle: const TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
      unselectedLabelStyle: const TextStyle(
        fontSize: 14,
        fontWeight: FontWeight.normal,
      ),
      tabs: tabs,
    );
  }

  // CARD DECORATION
  static BoxDecoration get cardDecoration => BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(borderRadiusL),
        boxShadow: shadowLight,
      );

  static BoxDecoration get gradientCardDecoration => BoxDecoration(
        gradient: AppColors.redToGreyGradient,
        borderRadius: BorderRadius.circular(borderRadiusL),
        boxShadow: shadowLight,
      );

  // BADGE DECORATION
  static BoxDecoration getBadgeDecoration({
    required Color color,
    bool isOutlined = false,
    double borderRadius = borderRadiusCircular,
  }) {
    return BoxDecoration(
      color: isOutlined ? Colors.white : color,
      borderRadius: BorderRadius.circular(borderRadius),
      border: Border.all(color: color, width: 1.5),
    );
  }

  // LAYOUTS
  static bool isNarrowScreen(BuildContext context) {
    return MediaQuery.of(context).size.width < 600;
  }

  static bool isSmallScreen(BuildContext context) {
    return MediaQuery.of(context).size.width < 400;
  }

  // DIVIDERS
  static Divider get standardDivider =>
      const Divider(height: 1, thickness: 1, color: AppColors.lightGrey);
}
