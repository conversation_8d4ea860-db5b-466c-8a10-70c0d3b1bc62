import 'package:flutter/material.dart';

class ThemeConstants {
  // Primary colors
  static const Color primary = Color(0xFF4A86E8);
  static const Color secondary = Color(0xFF6C757D);
  static const Color accent = Color(0xFF28A745);

  // Text colors
  static const Color black = Color(0xFF212121);
  static const Color darkGrey = Color(0xFF757575);
  static const Color lightGrey = Color(0xFFBDBDBD);
  static const Color white = Color(0xFFFFFFFF);

  // Feedback colors
  static const Color success = Color(0xFF28A745);
  static const Color warning = Color(0xFFFFC107);
  static const Color error = Color(0xFFDC3545);
  static const Color info = Color(0xFF17A2B8);

  // Background colors
  static const Color background = Color(0xFFF5F5F5);
  static const Color cardBackground = Color(0xFFFFFFFF);
  static const Color divider = Color(0xFFE0E0E0);

  // Responsiveness breakpoints
  static const double mobileBreakpoint = 600;
  static const double tabletBreakpoint = 900;
  static const double desktopBreakpoint = 1200;

  // Dynamic padding based on screen size
  static EdgeInsets getScreenPadding(BuildContext context) {
    final width = MediaQuery.of(context).size.width;

    if (width < mobileBreakpoint) {
      return const EdgeInsets.symmetric(horizontal: 16.0);
    } else if (width < tabletBreakpoint) {
      return const EdgeInsets.symmetric(horizontal: 24.0);
    } else {
      return const EdgeInsets.symmetric(horizontal: 32.0);
    }
  }

  // Dynamic font sizes based on screen size
  static double getTitleFontSize(BuildContext context) {
    return 20.0; // Consistent title font size across all devices
  }

  static double getBodyFontSize(BuildContext context) {
    final width = MediaQuery.of(context).size.width;

    if (width < mobileBreakpoint) {
      return 14.0;
    } else if (width < tabletBreakpoint) {
      return 16.0;
    } else {
      return 18.0;
    }
  }
}
