/// Payment data models for OnePay integration
/// Migrated and enhanced from legacy sso-flutter app

class PaymentTransaction {
  final String id;
  final String type;
  final String title;
  final String connectionNumber;
  final double amount;
  final String status;
  final DateTime date;
  final String operatorCode;
  final String? operatorName;
  final String? customerName;
  final String? transactionId;
  final String? referenceId;
  final Map<String, dynamic>? additionalData;

  PaymentTransaction({
    required this.id,
    required this.type,
    required this.title,
    required this.connectionNumber,
    required this.amount,
    required this.status,
    required this.date,
    required this.operatorCode,
    this.operatorName,
    this.customerName,
    this.transactionId,
    this.referenceId,
    this.additionalData,
  });

  factory PaymentTransaction.fromJson(Map<String, dynamic> json) {
    return PaymentTransaction(
      id: json['id']?.toString() ?? '',
      type: json['type'] ?? json['bill_type'] ?? '',
      title: json['title'] ?? json['service_name'] ?? '',
      connectionNumber: json['connection_number'] ?? json['consumer_number'] ?? '',
      amount: (json['amount'] ?? 0).toDouble(),
      status: json['status'] ?? 'pending',
      date: DateTime.tryParse(json['date'] ?? json['created_at'] ?? '') ?? DateTime.now(),
      operatorCode: json['operator_code'] ?? '',
      operatorName: json['operator_name'],
      customerName: json['customer_name'],
      transactionId: json['transaction_id'],
      referenceId: json['reference_id'],
      additionalData: json['additional_data'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type,
      'title': title,
      'connection_number': connectionNumber,
      'amount': amount,
      'status': status,
      'date': date.toIso8601String(),
      'operator_code': operatorCode,
      'operator_name': operatorName,
      'customer_name': customerName,
      'transaction_id': transactionId,
      'reference_id': referenceId,
      'additional_data': additionalData,
    };
  }
}

class RechargePlan {
  final String id;
  final String name;
  final double amount;
  final String validity;
  final String description;
  final String type; // 'topup', 'fulltt', 'data', etc.
  final List<String> benefits;

  RechargePlan({
    required this.id,
    required this.name,
    required this.amount,
    required this.validity,
    required this.description,
    required this.type,
    required this.benefits,
  });

  factory RechargePlan.fromJson(Map<String, dynamic> json) {
    return RechargePlan(
      id: json['id']?.toString() ?? '',
      name: json['name'] ?? json['plan_name'] ?? '',
      amount: (json['amount'] ?? json['price'] ?? 0).toDouble(),
      validity: json['validity'] ?? '',
      description: json['description'] ?? json['desc'] ?? '',
      type: json['type'] ?? json['plan_type'] ?? '',
      benefits: (json['benefits'] as List<dynamic>?)?.cast<String>() ?? [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'amount': amount,
      'validity': validity,
      'description': description,
      'type': type,
      'benefits': benefits,
    };
  }
}

class PaymentOperator {
  final String code;
  final String name;
  final String type;
  final String? logoUrl;
  final bool isActive;
  final List<String>? supportedStates;
  final Map<String, dynamic>? metadata;

  PaymentOperator({
    required this.code,
    required this.name,
    required this.type,
    this.logoUrl,
    required this.isActive,
    this.supportedStates,
    this.metadata,
  });

  factory PaymentOperator.fromJson(Map<String, dynamic> json) {
    return PaymentOperator(
      code: json['code'] ?? json['operator_code'] ?? '',
      name: json['name'] ?? json['operator_name'] ?? '',
      type: json['type'] ?? json['service_type'] ?? '',
      logoUrl: json['logo_url'] ?? json['image'],
      isActive: json['is_active'] ?? json['active'] ?? true,
      supportedStates: json['supported_states']?.cast<String>(),
      metadata: json['metadata'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'code': code,
      'name': name,
      'type': type,
      'logo_url': logoUrl,
      'is_active': isActive,
      'supported_states': supportedStates,
      'metadata': metadata,
    };
  }
}

class BillDetails {
  final String connectionNumber;
  final String customerName;
  final double amount;
  final DateTime dueDate;
  final String billNumber;
  final String operatorCode;
  final String operatorName;
  final String status;
  final Map<String, dynamic>? additionalInfo;

  BillDetails({
    required this.connectionNumber,
    required this.customerName,
    required this.amount,
    required this.dueDate,
    required this.billNumber,
    required this.operatorCode,
    required this.operatorName,
    required this.status,
    this.additionalInfo,
  });

  factory BillDetails.fromJson(Map<String, dynamic> json) {
    return BillDetails(
      connectionNumber: json['connection_number'] ?? json['consumer_number'] ?? '',
      customerName: json['customer_name'] ?? '',
      amount: (json['amount'] ?? json['bill_amount'] ?? 0).toDouble(),
      dueDate: DateTime.tryParse(json['due_date'] ?? '') ?? DateTime.now(),
      billNumber: json['bill_number'] ?? '',
      operatorCode: json['operator_code'] ?? '',
      operatorName: json['operator_name'] ?? '',
      status: json['status'] ?? 'pending',
      additionalInfo: json['additional_info'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'connection_number': connectionNumber,
      'customer_name': customerName,
      'amount': amount,
      'due_date': dueDate.toIso8601String(),
      'bill_number': billNumber,
      'operator_code': operatorCode,
      'operator_name': operatorName,
      'status': status,
      'additional_info': additionalInfo,
    };
  }
}

class PaymentRequest {
  final String billType;
  final String connectionNumber;
  final double amount;
  final String operatorCode;
  final String? customerName;
  final String? planId;
  final String? circle;
  final Map<String, dynamic>? additionalParams;

  PaymentRequest({
    required this.billType,
    required this.connectionNumber,
    required this.amount,
    required this.operatorCode,
    this.customerName,
    this.planId,
    this.circle,
    this.additionalParams,
  });

  Map<String, dynamic> toJson() {
    return {
      'bill_type': billType,
      'connection_number': connectionNumber,
      'amount': amount,
      'operator_code': operatorCode,
      if (customerName != null) 'customer_name': customerName,
      if (planId != null) 'plan_id': planId,
      if (circle != null) 'circle': circle,
      if (additionalParams != null) ...additionalParams!,
    };
  }
}

class PaymentResponse {
  final bool success;
  final String message;
  final String? transactionId;
  final String? referenceId;
  final String status;
  final double amount;
  final DateTime timestamp;
  final Map<String, dynamic>? data;

  PaymentResponse({
    required this.success,
    required this.message,
    this.transactionId,
    this.referenceId,
    required this.status,
    required this.amount,
    required this.timestamp,
    this.data,
  });

  factory PaymentResponse.fromJson(Map<String, dynamic> json) {
    return PaymentResponse(
      success: json['success'] ?? json['status'] == 'success',
      message: json['message'] ?? json['msg'] ?? '',
      transactionId: json['transaction_id'] ?? json['txn_id'],
      referenceId: json['reference_id'] ?? json['ref_id'],
      status: json['status'] ?? 'pending',
      amount: (json['amount'] ?? 0).toDouble(),
      timestamp: DateTime.tryParse(json['timestamp'] ?? json['created_at'] ?? '') ?? DateTime.now(),
      data: json['data'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'message': message,
      'transaction_id': transactionId,
      'reference_id': referenceId,
      'status': status,
      'amount': amount,
      'timestamp': timestamp.toIso8601String(),
      'data': data,
    };
  }
}

/// Payment service types enum
enum PaymentServiceType {
  mobileRecharge('mobile_prepaid', 'Mobile Recharge'),
  mobilePostpaid('mobile_postpaid', 'Mobile Postpaid'),
  dthRecharge('dth', 'DTH Recharge'),
  electricity('electricity', 'Electricity Bill'),
  water('water', 'Water Bill'),
  gas('gas', 'Gas Bill'),
  broadband('broadband', 'Broadband Bill'),
  landline('landline', 'Landline Bill'),
  insurance('insurance', 'Insurance'),
  emi('emi', 'EMI Payment'),
  rent('rent', 'Rent Payment'),
  maintenance('maintenance', 'Maintenance');

  const PaymentServiceType(this.code, this.displayName);
  
  final String code;
  final String displayName;
}
