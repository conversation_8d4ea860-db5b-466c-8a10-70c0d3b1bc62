class AppConstants {
  // App Info
  static const String appName = 'Society Management App';
  static const String appVersion = '1.0.0';

  // API URLs - Using real sso-flutter production endpoints
  static const String apiBaseUrl = 'https://api.chsone.in/residentapi/v2';

  // Navigation Routes
  static const String routeLogin = '/login';
  static const String routeDashboard = '/dashboard';
  static const String routeHousehold = '/household';
  static const String routePayments = '/payments';
  static const String routeMeetings = '/meetings';
  static const String routeWork = '/work';
  static const String routeProfile = '/profile';

  // Dashboard Tab Indices
  static const int tabHousehold = 0;
  static const int tabPayments = 1;
  static const int tabWork = 2;
  static const int tabProfile = 3;

  // Mock Data Keys
  static const String keyUser = 'user_data';
  static const String keySocieties = 'societies_data';
  static const String keyNotices = 'notices_data';
  static const String keyComplaints = 'complaints_data';
  static const String keyVisitors = 'visitors_data';
  static const String keyStaff = 'staff_data';
}
