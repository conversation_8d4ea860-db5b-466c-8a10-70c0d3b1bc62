import 'package:dio/dio.dart';
import 'package:oneapp/core/constants.dart';

class SsoApiService {
  static const String kongBaseUrl = AppConstants.apiBaseUrl;
  final Dio _dio = Dio();

  // Login
  Future<Response> login({required String username, required String password}) async {
    final url = '$kongBaseUrl/users/login';
    final data = {
      'username': username,
      'password': password,
      // Add other required fields as needed
    };
    return await _dio.post(url, data: data);
  }

  // Signup
  Future<Response> signup({
    required String username,
    required String password,
    required String firstName,
    required String lastName,
    // Add other required fields as needed
  }) async {
    final url = '$kongBaseUrl/users';
    final data = {
      'username': username,
      'password': password,
      'first_name': firstName,
      'last_name': lastName,
      // Add other required fields as needed
    };
    return await _dio.post(url, data: data);
  }

  // Get Profile
  Future<Response> getProfile(String accessToken) async {
    final url = '$kongBaseUrl/users/profile';
    return await _dio.get(url, options: Options(headers: {'Authorization': 'Bearer $accessToken'}));
  }

  // Update Profile
  Future<Response> updateProfile({
    required String accessToken,
    required Map<String, dynamic> profileData,
  }) async {
    final url = '$kongBaseUrl/users/profile';
    return await _dio.put(url, data: profileData, options: Options(headers: {'Authorization': 'Bearer $accessToken'}));
  }

  // Upload Avatar
  Future<Response> uploadAvatar({
    required String accessToken,
    required String filePath,
  }) async {
    final url = '$kongBaseUrl/users/avatars';
    final formData = FormData.fromMap({
      'image': await MultipartFile.fromFile(filePath, filename: 'avatar.jpg'),
    });
    return await _dio.post(url, data: formData, options: Options(headers: {'Authorization': 'Bearer $accessToken'}));
  }

  // Get Countries
  Future<Response> getCountries(String accessToken) async {
    final url = '$kongBaseUrl/countries';
    return await _dio.get(url, options: Options(headers: {'Authorization': 'Bearer $accessToken'}));
  }

  // Get States by Country
  Future<Response> getStates({
    required String accessToken,
    required String countryId,
  }) async {
    final url = '$kongBaseUrl/countries/$countryId/states';
    return await _dio.get(url, options: Options(headers: {'Authorization': 'Bearer $accessToken'}));
  }

  // Search User
  Future<Response> searchUser({required String query}) async {
    final url = '$kongBaseUrl/users/search';
    return await _dio.get(url, queryParameters: {'q': query});
  }

  // Send OTP
  Future<Response> sendOtp({required Map<String, dynamic> data}) async {
    final url = '$kongBaseUrl/users/otp';
    return await _dio.post(url, data: data);
  }
  // Verify OTP
  // In lib/core/services/sso_api_service.dart
// In lib/core/services/sso_api_service.dart
  Future<Response> verifyOtp({required Map<String, dynamic> data}) async {
    final url = '$kongBaseUrl/users/verifyotp';
    return await _dio.post(url, data: data);
  }

  // Reset Password
  Future<Response> resetPassword({required Map<String, dynamic> data}) async {
    final url = '$kongBaseUrl/users/resetpassword';
    return await _dio.post(url, data: data);
  }

  // Set/Change Password
  Future<Response> setPassword({required Map<String, dynamic> data}) async {
    final url = '$kongBaseUrl/users/passwords';
    return await _dio.post(url, data: data);
  }

  // Add User Address
  Future<Response> addUserAddress({required Map<String, dynamic> data}) async {
    final url = '$kongBaseUrl/user/addresses';
    return await _dio.post(url, data: data);
  }

  // Send Contact OTP
  Future<Response> sendContactOtp({required Map<String, dynamic> data}) async {
    final url = '$kongBaseUrl/users/contacts/otp';
    return await _dio.post(url, data: data);
  }

  // Verify Contact OTP
  Future<Response> verifyContactOtp({required Map<String, dynamic> data}) async {
    final url = '$kongBaseUrl/users/contacts/verifyotp';
    return await _dio.post(url, data: data);
  }

  // Get Profile Progress
  Future<Response> getProfileProgress(String accessToken) async {
    final url = '$kongBaseUrl/users/profile-progress';
    return await _dio.get(url, options: Options(headers: {'Authorization': 'Bearer $accessToken'}));
  }

  // Society Search
  Future<Response> searchSociety({required String query}) async {
    final url = '$kongBaseUrl/soceity/search';
    return await _dio.get(url, queryParameters: {'q': query});
  }

  // Get Buildings
  Future<Response> getBuildings({required String accessToken}) async {
    final url = '$kongBaseUrl/users/properties/buildings';
    return await _dio.get(url, options: Options(headers: {'Authorization': 'Bearer $accessToken'}));
  }

  // Get Units
  Future<Response> getUnits({required String accessToken}) async {
    final url = '$kongBaseUrl/users/properties/units';
    return await _dio.get(url, options: Options(headers: {'Authorization': 'Bearer $accessToken'}));
  }

  // Get Member Types
  Future<Response> getMemberTypes({required String accessToken}) async {
    final url = '$kongBaseUrl/users/member-types';
    return await _dio.get(url, options: Options(headers: {'Authorization': 'Bearer $accessToken'}));
  }

  // Get Member Status
  Future<Response> getMemberStatus({required String accessToken}) async {
    final url = '$kongBaseUrl/users/get-member-status';
    return await _dio.get(url, options: Options(headers: {'Authorization': 'Bearer $accessToken'}));
  }

  // Get Unit Member Details
  Future<Response> getUnitMemberDetails({required String accessToken}) async {
    final url = '$kongBaseUrl/users/get-unit-member-details';
    return await _dio.get(url, options: Options(headers: {'Authorization': 'Bearer $accessToken'}));
  }

  // Get Receipts
  Future<Response> getReceipts({required String accessToken}) async {
    final url = '$kongBaseUrl/receipts';
    return await _dio.get(url, options: Options(headers: {'Authorization': 'Bearer $accessToken'}));
  }

  // Get Invoices
  Future<Response> getInvoices({required String accessToken}) async {
    final url = '$kongBaseUrl/maintenance/invoices';
    return await _dio.get(url, options: Options(headers: {'Authorization': 'Bearer $accessToken'}));
  }

  // Get Incidental Invoices
  Future<Response> getIncidentalInvoices({required String accessToken}) async {
    final url = '$kongBaseUrl/incident/invoices';
    return await _dio.get(url, options: Options(headers: {'Authorization': 'Bearer $accessToken'}));
  }

  // Register Member
  Future<Response> registerMember({required Map<String, dynamic> data}) async {
    final url = '$kongBaseUrl/users/register-member-without-type';
    return await _dio.post(url, data: data);
  }
}