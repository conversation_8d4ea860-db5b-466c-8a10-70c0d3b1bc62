import 'dart:convert';
import 'dart:developer';
import 'package:dio/dio.dart';
import 'keycloak_service.dart';

/// OnePay API Service for payment operations
/// Migrated from legacy sso-flutter app with enhanced architecture
class OnePayApiService {
  static OnePayApiService? _instance;
  final Dio _dio = Dio();

  // OnePay API Configuration (from legacy app production config)
  static const String onePayBaseUrl = 'https://apigw.cubeone.in/onepay-live/api/v2/';
  static const String paymentHistoryUrl = 'https://apigw.cubeone.in/onepay/api/v1/listutilitybillstxns';
  
  // Mobikwik Configuration (from legacy app)
  static const String mobikwikBaseUrl = 'https://b2b.mobikwik.com/';
  static const String mobikwikMerchantId = 'fs101';

  // Singleton instance
  static OnePayApiService get instance {
    _instance ??= OnePayApiService._();
    return _instance!;
  }

  OnePayApiService._() {
    _setupDioInterceptors();
  }

  /// Setup Dio interceptors for logging and error handling
  void _setupDioInterceptors() {
    _dio.interceptors.add(
      InterceptorsWrapper(
        onRequest: (options, handler) {
          log('🚀 OnePay API Request: ${options.method} ${options.uri}');
          if (options.data != null) {
            log('📤 Request Data: ${options.data}');
          }
          handler.next(options);
        },
        onResponse: (response, handler) {
          log('✅ OnePay API Response: ${response.statusCode} ${response.requestOptions.uri}');
          log('📥 Response Data: ${response.data}');
          handler.next(response);
        },
        onError: (error, handler) {
          log('❌ OnePay API Error: ${error.message}');
          if (error.response != null) {
            log('📥 Error Response: ${error.response?.data}');
          }
          handler.next(error);
        },
      ),
    );
  }

  /// Get headers with authentication token
  Future<Map<String, String>> _getHeaders() async {
    final token = await KeycloakService.getAccessToken();
    
    if (token == null) {
      throw Exception('Authentication token not found');
    }

    return {
      'Content-Type': 'application/json; charset=utf-8',
      'Accept': 'application/json',
      'Authorization': 'Bearer $token',
    };
  }

  /// Get recharge plans for mobile/DTH
  /// Endpoint: /rechargePlansAPI/{operator}/{circle}
  Future<Map<String, dynamic>> getRechargePlans({
    required String operator,
    required String circle,
  }) async {
    try {
      final headers = await _getHeaders();
      final url = '${onePayBaseUrl}rechargePlansAPI/$operator/$circle';
      
      final response = await _dio.get(
        url,
        options: Options(headers: headers),
      );

      return response.data as Map<String, dynamic>;
    } catch (e) {
      log('Error getting recharge plans: $e');
      rethrow;
    }
  }

  /// Get connection details for a number
  /// Endpoint: /getconnectiondetails?cn={number}
  Future<Map<String, dynamic>> getConnectionDetails({
    required String connectionNumber,
  }) async {
    try {
      final headers = await _getHeaders();
      final url = '${onePayBaseUrl}getconnectiondetails';
      
      final response = await _dio.get(
        url,
        queryParameters: {'cn': connectionNumber},
        options: Options(headers: headers),
      );

      return response.data as Map<String, dynamic>;
    } catch (e) {
      log('Error getting connection details: $e');
      rethrow;
    }
  }

  /// Process utility bill payment
  /// Endpoint: /utilitybills/pay
  Future<Map<String, dynamic>> payUtilityBill({
    required String billType,
    required String connectionNumber,
    required double amount,
    required String operatorCode,
    String? customerName,
    Map<String, dynamic>? additionalParams,
  }) async {
    try {
      final headers = await _getHeaders();
      final url = '${onePayBaseUrl}utilitybills/pay';
      
      final requestData = {
        'bill_type': billType,
        'connection_number': connectionNumber,
        'amount': amount,
        'operator_code': operatorCode,
        if (customerName != null) 'customer_name': customerName,
        if (additionalParams != null) ...additionalParams,
      };

      final response = await _dio.post(
        url,
        data: requestData,
        options: Options(headers: headers),
      );

      return response.data as Map<String, dynamic>;
    } catch (e) {
      log('Error processing utility bill payment: $e');
      rethrow;
    }
  }

  /// Get payment history
  /// Endpoint: /listutilitybillstxns
  Future<Map<String, dynamic>> getPaymentHistory({
    int? limit,
    int? offset,
    String? fromDate,
    String? toDate,
  }) async {
    try {
      final headers = await _getHeaders();
      
      final queryParams = <String, dynamic>{};
      if (limit != null) queryParams['limit'] = limit;
      if (offset != null) queryParams['offset'] = offset;
      if (fromDate != null) queryParams['from_date'] = fromDate;
      if (toDate != null) queryParams['to_date'] = toDate;

      final response = await _dio.get(
        paymentHistoryUrl,
        queryParameters: queryParams,
        options: Options(headers: headers),
      );

      return response.data as Map<String, dynamic>;
    } catch (e) {
      log('Error getting payment history: $e');
      rethrow;
    }
  }

  /// Process mobile recharge
  Future<Map<String, dynamic>> processMobileRecharge({
    required String mobileNumber,
    required double amount,
    required String operatorCode,
    required String circle,
    String? planId,
  }) async {
    return await payUtilityBill(
      billType: 'mobile_prepaid',
      connectionNumber: mobileNumber,
      amount: amount,
      operatorCode: operatorCode,
      additionalParams: {
        'circle': circle,
        if (planId != null) 'plan_id': planId,
      },
    );
  }

  /// Process DTH recharge
  Future<Map<String, dynamic>> processDthRecharge({
    required String subscriberId,
    required double amount,
    required String operatorCode,
  }) async {
    return await payUtilityBill(
      billType: 'dth',
      connectionNumber: subscriberId,
      amount: amount,
      operatorCode: operatorCode,
    );
  }

  /// Process electricity bill payment
  Future<Map<String, dynamic>> processElectricityBill({
    required String consumerNumber,
    required double amount,
    required String operatorCode,
    String? customerName,
  }) async {
    return await payUtilityBill(
      billType: 'electricity',
      connectionNumber: consumerNumber,
      amount: amount,
      operatorCode: operatorCode,
      customerName: customerName,
    );
  }

  /// Process water bill payment
  Future<Map<String, dynamic>> processWaterBill({
    required String consumerNumber,
    required double amount,
    required String operatorCode,
    String? customerName,
  }) async {
    return await payUtilityBill(
      billType: 'water',
      connectionNumber: consumerNumber,
      amount: amount,
      operatorCode: operatorCode,
      customerName: customerName,
    );
  }

  /// Process gas bill payment
  Future<Map<String, dynamic>> processGasBill({
    required String consumerNumber,
    required double amount,
    required String operatorCode,
    String? customerName,
  }) async {
    return await payUtilityBill(
      billType: 'gas',
      connectionNumber: consumerNumber,
      amount: amount,
      operatorCode: operatorCode,
      customerName: customerName,
    );
  }

  /// Get available operators for a service type
  Future<Map<String, dynamic>> getOperators({
    required String serviceType,
    String? state,
  }) async {
    try {
      final headers = await _getHeaders();
      final url = '${onePayBaseUrl}operators';
      
      final queryParams = <String, dynamic>{
        'service_type': serviceType,
        if (state != null) 'state': state,
      };

      final response = await _dio.get(
        url,
        queryParameters: queryParams,
        options: Options(headers: headers),
      );

      return response.data as Map<String, dynamic>;
    } catch (e) {
      log('Error getting operators: $e');
      rethrow;
    }
  }

  /// Validate bill details before payment
  Future<Map<String, dynamic>> validateBill({
    required String billType,
    required String connectionNumber,
    required String operatorCode,
  }) async {
    try {
      final headers = await _getHeaders();
      final url = '${onePayBaseUrl}validate-bill';
      
      final requestData = {
        'bill_type': billType,
        'connection_number': connectionNumber,
        'operator_code': operatorCode,
      };

      final response = await _dio.post(
        url,
        data: requestData,
        options: Options(headers: headers),
      );

      return response.data as Map<String, dynamic>;
    } catch (e) {
      log('Error validating bill: $e');
      rethrow;
    }
  }
}
