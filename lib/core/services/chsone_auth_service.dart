import 'dart:convert';
import 'dart:developer';
import 'package:http/http.dart' as http;
import '../constants.dart';
import 'secure_storage_service.dart';
import 'keycloak_service.dart';

/// CHSONE Authentication Service
/// Handles CHSONE-specific authentication for maintenance APIs
class ChsoneAuthService {
  static final ChsoneAuthService _instance = ChsoneAuthService._internal();
  static ChsoneAuthService get instance => _instance;
  ChsoneAuthService._internal();

  final SecureStorageService _storage = SecureStorageService();
  static const String _chsoneTokenKey = 'chsone_access_token';
  static const String _chsoneUserDataKey = 'chsone_user_data';
  static const String _chsoneTokenExpiryKey = 'chsone_token_expiry';

  /// Get CHSONE access token
  /// Returns cached token if valid, otherwise attempts to get new token
  Future<String?> getChsoneAccessToken() async {
    try {
      log('🔑 [ChsoneAuth] ===== STARTING CHSONE AUTHENTICATION =====');
      log('🔑 [ChsoneA<PERSON>] Getting CHSONE access token...');

      // Check if we have a cached valid token
      final cachedToken = await _getCachedToken();
      if (cachedToken != null) {
        log('✅ [ChsoneAuth] Using cached CHSONE token: ${cachedToken.substring(0, 20)}...');
        return cachedToken;
      }

      // ALWAYS try to get new token (force login attempt)
      log('🔄 [ChsoneAuth] No cached token, attempting CHSONE login...');
      final newToken = await _performChsoneLogin();

      if (newToken != null && newToken.isNotEmpty) {
        log('✅ [ChsoneAuth] Successfully obtained new CHSONE token: ${newToken.substring(0, 20)}...');
        return newToken;
      }

      log('❌ [ChsoneAuth] Failed to obtain CHSONE token - will use fallback authentication');
      return null;
    } catch (e) {
      log('❌ [ChsoneAuth] Error getting CHSONE token: $e');
      log('📍 [ChsoneAuth] Stack trace: ${StackTrace.current}');
      return null;
    }
  }

  /// Get cached token if still valid
  Future<String?> _getCachedToken() async {
    try {
      final token = await _storage.read(key: _chsoneTokenKey);
      if (token == null) return null;

      // Check if token is expired
      final expiryStr = await _storage.read(key: _chsoneTokenExpiryKey);
      if (expiryStr != null) {
        final expiry = DateTime.parse(expiryStr);
        if (DateTime.now().isAfter(expiry)) {
          log('⏰ [ChsoneAuth] Cached token expired');
          return null;
        }
      }

      return token;
    } catch (e) {
      log('❌ [ChsoneAuth] Error checking cached token: $e');
      return null;
    }
  }

  /// Perform CHSONE login to get access token
  /// Uses real sso-flutter compatible authentication flow
  Future<String?> _performChsoneLogin() async {
    try {
      log('📡 [ChsoneAuth] ===== PERFORMING CHSONE LOGIN =====');
      log('📡 [ChsoneAuth] Using sso-flutter compatible authentication flow...');

      // Get user profile from OneApp authentication (similar to SsoStorage.getUserProfile())
      final userProfile = await _getUserProfileFromOneApp();
      if (userProfile == null) {
        log('❌ [ChsoneAuth] No user profile available for CHSONE login');
        return null;
      }

      log('👤 [ChsoneAuth] User profile extracted: ${userProfile.keys.toList()}');
      log('👤 [ChsoneAuth] Username: ${userProfile['username']}');
      log('👤 [ChsoneAuth] User ID: ${userProfile['user_id']}');
      log('👤 [ChsoneAuth] Email: ${userProfile['email']}');

      // Build sso-flutter compatible login parameters
      final loginData = {
        'username': userProfile['username'] ?? userProfile['email'] ?? 'demo_user',
        'user_id': userProfile['user_id']?.toString() ?? userProfile['id']?.toString() ?? '1',
        'grant_type': 'password',
        'session_token': userProfile['session_token'] ?? userProfile['access_token'] ?? 'demo_session',
        'soc_id': userProfile['soc_id']?.toString() ?? '1', // Default society ID
        // Production CHSONE credentials from sso-flutter
        'client_id': 'Epm9rkpUrbSSByBD',
        'client_secret': 'Nk68pdu6kLFHTAAH',
      };

      final url = '${AppConstants.apiBaseUrl}/users/token';
      log('🔗 [ChsoneAuth] CHSONE login URL: $url');
      log('🔑 [ChsoneAuth] Login parameters: ${loginData.keys.toList()}');
      log('🔑 [ChsoneAuth] Username: ${loginData['username']}');
      log('🔑 [ChsoneAuth] User ID: ${loginData['user_id']}');
      log('🔑 [ChsoneAuth] Society ID: ${loginData['soc_id']}');
      log('🔑 [ChsoneAuth] Client ID: ${loginData['client_id']}');

      // Use form-encoded data (sso-flutter uses HashMap which becomes form data)
      log('📡 [ChsoneAuth] Making HTTP POST request to CHSONE login API...');
      final response = await http.post(
        Uri.parse(url),
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'Accept': 'application/json',
        },
        body: loginData,
      );

      log('📊 [ChsoneAuth] ===== CHSONE LOGIN RESPONSE =====');
      log('📊 [ChsoneAuth] Response status: ${response.statusCode}');
      log('📊 [ChsoneAuth] Response headers: ${response.headers}');
      log('📊 [ChsoneAuth] Response body: ${response.body}');

      if (response.statusCode == 200) {
        log('✅ [ChsoneAuth] HTTP 200 - Parsing response...');
        final responseData = json.decode(response.body);
        log('📋 [ChsoneAuth] Parsed response data: $responseData');

        if (responseData['data'] != null &&
            responseData['data']['access_info'] != null) {

          final accessInfo = responseData['data']['access_info'];
          final accessToken = accessInfo['access_token'];

          log('🔑 [ChsoneAuth] Access info found: ${accessInfo.keys.toList()}');
          log('🔑 [ChsoneAuth] Access token preview: ${accessToken?.toString().substring(0, 20)}...');

          if (accessToken != null && accessToken.toString().isNotEmpty) {
            // Save token and user data
            await _saveChsoneTokenData(responseData['data']);
            log('✅ [ChsoneAuth] CHSONE login successful - token saved!');
            return accessToken.toString();
          } else {
            log('❌ [ChsoneAuth] Access token is null or empty');
          }
        } else {
          log('❌ [ChsoneAuth] Response missing data or access_info structure');
        }
      } else {
        log('❌ [ChsoneAuth] CHSONE login failed with status: ${response.statusCode}');
        log('❌ [ChsoneAuth] Error response: ${response.body}');
      }

      return null;
    } catch (e) {
      log('❌ [ChsoneAuth] CHSONE login error: $e');
      return null;
    }
  }

  /// Save CHSONE token and user data
  Future<void> _saveChsoneTokenData(Map<String, dynamic> data) async {
    try {
      final accessInfo = data['access_info'];
      final userData = data['user_data'];
      
      if (accessInfo != null && accessInfo['access_token'] != null) {
        // Save access token
        await _storage.write(
          key: _chsoneTokenKey,
          value: accessInfo['access_token'],
        );

        // Save token expiry (default to 24 hours if not provided)
        final expiryTime = DateTime.now().add(const Duration(hours: 24));
        await _storage.write(
          key: _chsoneTokenExpiryKey,
          value: expiryTime.toIso8601String(),
        );

        // Save user data
        if (userData != null) {
          await _storage.write(
            key: _chsoneUserDataKey,
            value: json.encode(userData),
          );
        }

        log('✅ [ChsoneAuth] CHSONE token data saved');
      }
    } catch (e) {
      log('❌ [ChsoneAuth] Error saving CHSONE token data: $e');
    }
  }

  /// Get CHSONE user data
  Future<Map<String, dynamic>?> getChsoneUserData() async {
    try {
      final userDataStr = await _storage.read(key: _chsoneUserDataKey);
      if (userDataStr != null) {
        return json.decode(userDataStr);
      }
      return null;
    } catch (e) {
      log('❌ [ChsoneAuth] Error getting CHSONE user data: $e');
      return null;
    }
  }

  /// Clear CHSONE authentication data
  Future<void> clearChsoneAuth() async {
    try {
      await _storage.write(key: _chsoneTokenKey, value: '');
      await _storage.write(key: _chsoneUserDataKey, value: '');
      await _storage.write(key: _chsoneTokenExpiryKey, value: '');
      log('✅ [ChsoneAuth] CHSONE auth data cleared');
    } catch (e) {
      log('❌ [ChsoneAuth] Error clearing CHSONE auth data: $e');
    }
  }

  /// Check if user is authenticated with CHSONE
  Future<bool> isChsoneAuthenticated() async {
    final token = await getChsoneAccessToken();
    return token != null && token.isNotEmpty;
  }

  /// Get user profile from OneApp authentication system
  /// This simulates SsoStorage.getUserProfile() from sso-flutter
  Future<Map<String, dynamic>?> _getUserProfileFromOneApp() async {
    try {
      log('👤 [ChsoneAuth] Getting user profile from OneApp...');

      // Try to get user data from Keycloak token
      final keycloakToken = await KeycloakService.getAccessToken();
      if (keycloakToken != null) {
        // Parse JWT token to extract user information
        final userInfo = _parseJWTToken(keycloakToken);
        if (userInfo != null) {
          log('✅ [ChsoneAuth] Extracted user info from Keycloak token');
          return {
            'username': userInfo['preferred_username'] ?? userInfo['email'] ?? 'demo_user',
            'email': userInfo['email'] ?? '<EMAIL>',
            'user_id': userInfo['sub'] ?? '1',
            'id': userInfo['sub'] ?? '1',
            'session_token': keycloakToken, // Use Keycloak token as session token
            'access_token': keycloakToken,
            'soc_id': '1', // Default society ID - in production, this would come from user selection
          };
        }
      }

      // Fallback to demo user profile for testing
      log('🔄 [ChsoneAuth] Using demo user profile for CHSONE login');
      return {
        'username': 'demo_user',
        'email': '<EMAIL>',
        'user_id': '1',
        'id': '1',
        'session_token': 'demo_session_token',
        'access_token': keycloakToken ?? 'demo_access_token',
        'soc_id': '1',
      };
    } catch (e) {
      log('❌ [ChsoneAuth] Error getting user profile: $e');
      return null;
    }
  }

  /// Parse JWT token to extract user information
  /// Simple JWT parser for extracting user claims
  Map<String, dynamic>? _parseJWTToken(String token) {
    try {
      final parts = token.split('.');
      if (parts.length != 3) return null;

      final payload = parts[1];
      // Add padding if needed
      final normalizedPayload = payload.padRight(
        (payload.length + 3) ~/ 4 * 4,
        '=',
      );

      final decoded = utf8.decode(base64Url.decode(normalizedPayload));
      return json.decode(decoded) as Map<String, dynamic>;
    } catch (e) {
      log('❌ [ChsoneAuth] Error parsing JWT token: $e');
      return null;
    }
  }
}
