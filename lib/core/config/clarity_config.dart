// Import for kDebugMode and LogLevel
import 'package:flutter/foundation.dart';
// import 'package:clarity_flutter/clarity_flutter.dart'; // Commented out until dependency is added

/// Configuration class for Microsoft Clarity integration
/// Update these values with your actual Clarity project settings
class ClarityAppConfig {
  // Clarity Project ID from https://clarity.microsoft.com/
  static const String projectId = 's1aidrf2ri';
  
  // Environment-specific settings
  static const bool enableInDebugMode = true; // Set to true for testing
  static const bool enableInReleaseMode = true;
  
  // Privacy and consent settings
  static const bool requireUserConsent = true;
  static const bool showConsentOnFirstLaunch = true;
  static const Duration consentDialogDelay = Duration(seconds: 3);
  
  // Session recording settings
  static const bool allowMeteredNetworkUsage = false;
  static const bool maskSensitiveContent = true;
  
  // Analytics settings
  static const bool trackScreenViews = true;
  static const bool trackUserActions = true;
  static const bool trackErrors = true;
  static const bool trackPerformance = true;
  
  // Data retention settings (informational - controlled by Clarity dashboard)
  static const int dataRetentionDays = 90;
  
  // Custom event names
  static const String eventAppLaunch = 'app_launch';
  static const String eventScreenView = 'screen_view';
  static const String eventUserAction = 'user_action';
  static const String eventFeatureUsage = 'feature_usage';
  static const String eventError = 'error';
  static const String eventFormSubmission = 'form_submission';
  static const String eventNavigation = 'navigation';
  static const String eventSearch = 'search';
  static const String eventButtonTap = 'button_tap';
  static const String eventUserLogin = 'user_login';
  static const String eventUserLogout = 'user_logout';
  
  // Session properties
  static const String propertyAppVersion = 'app_version';
  static const String propertyPlatform = 'platform';
  static const String propertyBuildMode = 'build_mode';
  static const String propertyUserId = 'user_id';
  static const String propertyUserType = 'user_type';
  static const String propertyDeviceType = 'device_type';
  
  // Privacy compliance URLs
  static const String privacyPolicyUrl = 'https://your-app.com/privacy';
  static const String clarityPrivacyUrl = 'https://privacy.microsoft.com/en-us/privacystatement';
  static const String dataProcessingUrl = 'https://your-app.com/data-processing';
  
  /// Check if Clarity should be enabled based on current environment
  static bool get shouldEnable {
    // In debug mode, only enable if explicitly configured
    if (kDebugMode) {
      return enableInDebugMode;
    }

    // In release mode, enable based on configuration
    return enableInReleaseMode;
  }

  /// Get log level based on environment
  // static LogLevel get logLevel {
  //   if (kDebugMode) {
  //     return LogLevel.Verbose;
  //   }
  //   return LogLevel.None;
  // }

  /// Validate configuration
  static bool get isConfigurationValid {
    // Check if project ID is set
    if (projectId == 'YOUR_CLARITY_PROJECT_ID' || projectId.isEmpty) {
      return false;
    }

    // Add other validation rules as needed
    return true;
  }
  
  /// Get configuration warnings
  static List<String> get configurationWarnings {
    final warnings = <String>[];

    if (projectId == 'YOUR_CLARITY_PROJECT_ID') {
      warnings.add('Clarity Project ID not configured');
    }

    if (enableInDebugMode && kDebugMode) {
      warnings.add('Clarity enabled in debug mode - ensure this is intentional');
    }

    if (!requireUserConsent) {
      warnings.add('User consent not required - ensure this complies with privacy regulations');
    }

    return warnings;
  }

  /// Print configuration status
  static void printConfigurationStatus() {
    debugPrint('=== Clarity Configuration ===');
    debugPrint('Project ID: ${projectId.replaceRange(8, null, '***')}');
    debugPrint('Should Enable: $shouldEnable');
    debugPrint('Debug Mode: $kDebugMode');
    debugPrint('Require Consent: $requireUserConsent');
    debugPrint('Configuration Valid: $isConfigurationValid');

    final warnings = configurationWarnings;
    if (warnings.isNotEmpty) {
      debugPrint('Warnings:');
      for (final warning in warnings) {
        debugPrint('  - $warning');
      }
    }
    debugPrint('============================');
  }
}
