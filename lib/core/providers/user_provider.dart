import 'package:flutter/material.dart';

import '../models/user_model.dart';
import '../services/keycloak_service.dart';
import '../services/secure_storage_service.dart';
import '../services/sso_api_service.dart';

class UserProvider extends ChangeNotifier {
  UserModel? _user;
  bool _isLoading = false;
  String? _errorMessage;
  final _secureStorage = SecureStorageService();
  final SsoApiService _ssoApi = SsoApiService();

  UserModel? get user => _user;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  bool get isLoggedIn => _user != null;

  // Method to set loading state
  void setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  UserProvider() {
    _initializeUser();
  }

  Future<void> _initializeUser() async {
    // Check if we have a Keycloak session
    final isAuthenticated = await KeycloakService.isAuthenticated();
    if (isAuthenticated) {
      final userData = await _secureStorage.getKeycloakUserData();
      if (userData != null) {
        _user = UserModel(
          id: userData['old_sso_user_id'] as String? ?? '',
          email: userData['email'] as String? ?? '',
          fullName: userData['name'] as String? ??
              '${userData['given_name'] ?? ''} ${userData['family_name'] ?? ''}',
          createdAt: DateTime.now(),
          authProvider: 'keycloak',
        );
      }
      notifyListeners();
    }
  }

  Future<bool> signInWithKeycloak() async {
    try {
      _isLoading = true;
      _errorMessage = null;
      notifyListeners();

      final success = await KeycloakService.login();

      if (success) {
        // Get user info from secure storage
        final userData = await KeycloakService.getUserData();

        if (userData != null) {
          _user = UserModel(
            id: userData['old_sso_user_id'] as String? ?? '',
            email: userData['email'] as String? ?? '',
            fullName: userData['name'] as String? ??
                '${userData['given_name'] ?? ''} ${userData['family_name'] ?? ''}',
            createdAt: DateTime.now(),
            authProvider: 'keycloak',
          );

          notifyListeners();
          return true;
        }
      }

      _errorMessage = 'Failed to sign in with Keycloak';
      return false;
    } catch (e) {
      _errorMessage = 'Failed to sign in with Keycloak: $e';
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> signOut() async {
    try {
      _isLoading = true;
      notifyListeners();

      await KeycloakService.logout();
      _user = null;
    } catch (e) {
      _errorMessage = 'Failed to sign out: $e';
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<bool> updateProfile({
    String? fullName,
    String? phone,
    String? profilePicture,
    String? selectedSocietyId,
    String? flatNumber,
  }) async {
    if (_user == null) return false;

    try {
      _isLoading = true;
      notifyListeners();

      // Update local user object
      _user = _user!.copyWith(
        fullName: fullName ?? _user!.fullName,
        phone: phone ?? _user!.phone,
        profilePicture: profilePicture ?? _user!.profilePicture,
        selectedSocietyId: selectedSocietyId ?? _user!.selectedSocietyId,
        flatNumber: flatNumber ?? _user!.flatNumber,
      );

      return true;
    } catch (e) {
      _errorMessage = 'Failed to update profile: $e';
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<String?> getAuthToken() async {
    // Check for Keycloak token
    return await KeycloakService.getAccessToken();
  }

  // SSO Login
  Future<bool> ssoLogin(String username, String password) async {
    setLoading(true);
    try {
      final response = await _ssoApi.login(username: username, password: password);
      if (response.statusCode == 200) {
        // Parse and set user as needed
        // _user = UserModel.fromJson(response.data);
        notifyListeners();
        return true;
      } else {
        _errorMessage = response.data.toString();
        notifyListeners();
        return false;
      }
    } catch (e) {
      _errorMessage = e.toString();
      notifyListeners();
      return false;
    } finally {
      setLoading(false);
    }
  }

  // SSO Signup
  Future<bool> ssoSignup({
    required String username,
    required String password,
    required String firstName,
    required String lastName,
  }) async {
    setLoading(true);
    try {
      final response = await _ssoApi.signup(
        username: username,
        password: password,
        firstName: firstName,
        lastName: lastName,
      );
      if (response.statusCode == 200) {
        // Parse and set user as needed
        notifyListeners();
        return true;
      } else {
        _errorMessage = response.data.toString();
        notifyListeners();
        return false;
      }
    } catch (e) {
      _errorMessage = e.toString();
      notifyListeners();
      return false;
    } finally {
      setLoading(false);
    }
  }

  // SSO Get Profile
  Future<Map<String, dynamic>?> ssoGetProfile() async {
    setLoading(true);
    try {
      final token = await getAuthToken();
      if (token == null) throw Exception('No auth token');
      final response = await _ssoApi.getProfile(token);
      if (response.statusCode == 200) {
        notifyListeners();
        return response.data;
      } else {
        _errorMessage = response.data.toString();
        notifyListeners();
        return null;
      }
    } catch (e) {
      _errorMessage = e.toString();
      notifyListeners();
      return null;
    } finally {
      setLoading(false);
    }
  }

  // SSO Update Profile
  Future<bool> ssoUpdateProfile(Map<String, dynamic> profileData) async {
    setLoading(true);
    try {
      final token = await getAuthToken();
      if (token == null) throw Exception('No auth token');
      final response = await _ssoApi.updateProfile(accessToken: token, profileData: profileData);
      if (response.statusCode == 200) {
        notifyListeners();
        return true;
      } else {
        _errorMessage = response.data.toString();
        notifyListeners();
        return false;
      }
    } catch (e) {
      _errorMessage = e.toString();
      notifyListeners();
      return false;
    } finally {
      setLoading(false);
    }
  }

  // SSO Avatar Upload
  Future<bool> ssoUploadAvatar(String filePath) async {
    setLoading(true);
    try {
      final token = await getAuthToken();
      if (token == null) throw Exception('No auth token');
      final response = await _ssoApi.uploadAvatar(accessToken: token, filePath: filePath);
      if (response.statusCode == 200) {
        notifyListeners();
        return true;
      } else {
        _errorMessage = response.data.toString();
        notifyListeners();
        return false;
      }
    } catch (e) {
      _errorMessage = e.toString();
      notifyListeners();
      return false;
    } finally {
      setLoading(false);
    }
  }

  // SSO Get Countries
  Future<List<dynamic>?> ssoGetCountries() async {
    setLoading(true);
    try {
      final token = await getAuthToken();
      if (token == null) throw Exception('No auth token');
      final response = await _ssoApi.getCountries(token);
      if (response.statusCode == 200) {
        notifyListeners();
        return response.data;
      } else {
        _errorMessage = response.data.toString();
        notifyListeners();
        return null;
      }
    } catch (e) {
      _errorMessage = e.toString();
      notifyListeners();
      return null;
    } finally {
      setLoading(false);
    }
  }

  // SSO Get States by Country
  Future<List<dynamic>?> ssoGetStates(String countryId) async {
    setLoading(true);
    try {
      final token = await getAuthToken();
      if (token == null) throw Exception('No auth token');
      final response = await _ssoApi.getStates(accessToken: token, countryId: countryId);
      if (response.statusCode == 200) {
        notifyListeners();
        return response.data;
      } else {
        _errorMessage = response.data.toString();
        notifyListeners();
        return null;
      }
    } catch (e) {
      _errorMessage = e.toString();
      notifyListeners();
      return null;
    } finally {
      setLoading(false);
    }
  }

  // SSO Search User
  Future<Map<String, dynamic>?> ssoSearchUser(String query) async {
    setLoading(true);
    try {
      final response = await _ssoApi.searchUser(query: query);
      if (response.statusCode == 200) {
        notifyListeners();
        return response.data;
      } else {
        _errorMessage = response.data.toString();
        notifyListeners();
        return null;
      }
    } catch (e) {
      _errorMessage = e.toString();
      notifyListeners();
      return null;
    } finally {
      setLoading(false);
    }
  }

  // SSO Send OTP
  Future<bool> ssoSendOtp(Map<String, dynamic> data) async {
    setLoading(true);
    try {
      final response = await _ssoApi.sendOtp(data: data);
      if (response.statusCode == 200) {
        notifyListeners();
        return true;
      } else {
        _errorMessage = response.data.toString();
        notifyListeners();
        return false;
      }
    } catch (e) {
      _errorMessage = e.toString();
      notifyListeners();
      return false;
    } finally {
      setLoading(false);
    }
  }

  // SSO Verify OTP
  Future<bool> ssoVerifyOtp(Map<String, dynamic> data) async {
    setLoading(true);
    try {
      final response = await _ssoApi.verifyOtp(data: data);
      if (response.statusCode == 200) {
        notifyListeners();
        return true;
      } else {
        _errorMessage = response.data.toString();
        notifyListeners();
        return false;
      }
    } catch (e) {
      _errorMessage = e.toString();
      notifyListeners();
      return false;
    } finally {
      setLoading(false);
    }
  }

  // SSO Reset Password
  Future<bool> ssoResetPassword(Map<String, dynamic> data) async {
    setLoading(true);
    try {
      final response = await _ssoApi.resetPassword(data: data);
      if (response.statusCode == 200) {
        notifyListeners();
        return true;
      } else {
        _errorMessage = response.data.toString();
        notifyListeners();
        return false;
      }
    } catch (e) {
      _errorMessage = e.toString();
      notifyListeners();
      return false;
    } finally {
      setLoading(false);
    }
  }

  // SSO Set/Change Password
  Future<bool> ssoSetPassword(Map<String, dynamic> data) async {
    setLoading(true);
    try {
      final response = await _ssoApi.setPassword(data: data);
      if (response.statusCode == 200) {
        notifyListeners();
        return true;
      } else {
        _errorMessage = response.data.toString();
        notifyListeners();
        return false;
      }
    } catch (e) {
      _errorMessage = e.toString();
      notifyListeners();
      return false;
    } finally {
      setLoading(false);
    }
  }

  // SSO Add User Address
  Future<bool> ssoAddUserAddress(Map<String, dynamic> data) async {
    setLoading(true);
    try {
      final response = await _ssoApi.addUserAddress(data: data);
      if (response.statusCode == 200) {
        notifyListeners();
        return true;
      } else {
        _errorMessage = response.data.toString();
        notifyListeners();
        return false;
      }
    } catch (e) {
      _errorMessage = e.toString();
      notifyListeners();
      return false;
    } finally {
      setLoading(false);
    }
  }

  // SSO Send Contact OTP
  Future<bool> ssoSendContactOtp(Map<String, dynamic> data) async {
    setLoading(true);
    try {
      final response = await _ssoApi.sendContactOtp(data: data);
      if (response.statusCode == 200) {
        notifyListeners();
        return true;
      } else {
        _errorMessage = response.data.toString();
        notifyListeners();
        return false;
      }
    } catch (e) {
      _errorMessage = e.toString();
      notifyListeners();
      return false;
    } finally {
      setLoading(false);
    }
  }

  // SSO Verify Contact OTP
  Future<bool> ssoVerifyContactOtp(Map<String, dynamic> data) async {
    setLoading(true);
    try {
      final response = await _ssoApi.verifyContactOtp(data: data);
      if (response.statusCode == 200) {
        notifyListeners();
        return true;
      } else {
        _errorMessage = response.data.toString();
        notifyListeners();
        return false;
      }
    } catch (e) {
      _errorMessage = e.toString();
      notifyListeners();
      return false;
    } finally {
      setLoading(false);
    }
  }

  // SSO Get Profile Progress
  Future<Map<String, dynamic>?> ssoGetProfileProgress() async {
    setLoading(true);
    try {
      final token = await getAuthToken();
      if (token == null) throw Exception('No auth token');
      final response = await _ssoApi.getProfileProgress(token);
      if (response.statusCode == 200) {
        notifyListeners();
        return response.data;
      } else {
        _errorMessage = response.data.toString();
        notifyListeners();
        return null;
      }
    } catch (e) {
      _errorMessage = e.toString();
      notifyListeners();
      return null;
    } finally {
      setLoading(false);
    }
  }

  // SSO Society Search
  Future<Map<String, dynamic>?> ssoSearchSociety(String query) async {
    setLoading(true);
    try {
      final response = await _ssoApi.searchSociety(query: query);
      if (response.statusCode == 200) {
        notifyListeners();
        return response.data;
      } else {
        _errorMessage = response.data.toString();
        notifyListeners();
        return null;
      }
    } catch (e) {
      _errorMessage = e.toString();
      notifyListeners();
      return null;
    } finally {
      setLoading(false);
    }
  }

  // SSO Get Buildings
  Future<Map<String, dynamic>?> ssoGetBuildings() async {
    setLoading(true);
    try {
      final token = await getAuthToken();
      if (token == null) throw Exception('No auth token');
      final response = await _ssoApi.getBuildings(accessToken: token);
      if (response.statusCode == 200) {
        notifyListeners();
        return response.data;
      } else {
        _errorMessage = response.data.toString();
        notifyListeners();
        return null;
      }
    } catch (e) {
      _errorMessage = e.toString();
      notifyListeners();
      return null;
    } finally {
      setLoading(false);
    }
  }

  // SSO Get Units
  Future<Map<String, dynamic>?> ssoGetUnits() async {
    setLoading(true);
    try {
      final token = await getAuthToken();
      if (token == null) throw Exception('No auth token');
      final response = await _ssoApi.getUnits(accessToken: token);
      if (response.statusCode == 200) {
        notifyListeners();
        return response.data;
      } else {
        _errorMessage = response.data.toString();
        notifyListeners();
        return null;
      }
    } catch (e) {
      _errorMessage = e.toString();
      notifyListeners();
      return null;
    } finally {
      setLoading(false);
    }
  }

  // SSO Get Member Types
  Future<Map<String, dynamic>?> ssoGetMemberTypes() async {
    setLoading(true);
    try {
      final token = await getAuthToken();
      if (token == null) throw Exception('No auth token');
      final response = await _ssoApi.getMemberTypes(accessToken: token);
      if (response.statusCode == 200) {
        notifyListeners();
        return response.data;
      } else {
        _errorMessage = response.data.toString();
        notifyListeners();
        return null;
      }
    } catch (e) {
      _errorMessage = e.toString();
      notifyListeners();
      return null;
    } finally {
      setLoading(false);
    }
  }

  // SSO Get Member Status
  Future<Map<String, dynamic>?> ssoGetMemberStatus() async {
    setLoading(true);
    try {
      final token = await getAuthToken();
      if (token == null) throw Exception('No auth token');
      final response = await _ssoApi.getMemberStatus(accessToken: token);
      if (response.statusCode == 200) {
        notifyListeners();
        return response.data;
      } else {
        _errorMessage = response.data.toString();
        notifyListeners();
        return null;
      }
    } catch (e) {
      _errorMessage = e.toString();
      notifyListeners();
      return null;
    } finally {
      setLoading(false);
    }
  }

  // SSO Get Unit Member Details
  Future<Map<String, dynamic>?> ssoGetUnitMemberDetails() async {
    setLoading(true);
    try {
      final token = await getAuthToken();
      if (token == null) throw Exception('No auth token');
      final response = await _ssoApi.getUnitMemberDetails(accessToken: token);
      if (response.statusCode == 200) {
        notifyListeners();
        return response.data;
      } else {
        _errorMessage = response.data.toString();
        notifyListeners();
        return null;
      }
    } catch (e) {
      _errorMessage = e.toString();
      notifyListeners();
      return null;
    } finally {
      setLoading(false);
    }
  }

  // SSO Get Receipts
  Future<Map<String, dynamic>?> ssoGetReceipts() async {
    setLoading(true);
    try {
      final token = await getAuthToken();
      if (token == null) throw Exception('No auth token');
      final response = await _ssoApi.getReceipts(accessToken: token);
      if (response.statusCode == 200) {
        notifyListeners();
        return response.data;
      } else {
        _errorMessage = response.data.toString();
        notifyListeners();
        return null;
      }
    } catch (e) {
      _errorMessage = e.toString();
      notifyListeners();
      return null;
    } finally {
      setLoading(false);
    }
  }

  // SSO Get Invoices
  Future<Map<String, dynamic>?> ssoGetInvoices() async {
    setLoading(true);
    try {
      final token = await getAuthToken();
      if (token == null) throw Exception('No auth token');
      final response = await _ssoApi.getInvoices(accessToken: token);
      if (response.statusCode == 200) {
        notifyListeners();
        return response.data;
      } else {
        _errorMessage = response.data.toString();
        notifyListeners();
        return null;
      }
    } catch (e) {
      _errorMessage = e.toString();
      notifyListeners();
      return null;
    } finally {
      setLoading(false);
    }
  }

  // SSO Get Incidental Invoices
  Future<Map<String, dynamic>?> ssoGetIncidentalInvoices() async {
    setLoading(true);
    try {
      final token = await getAuthToken();
      if (token == null) throw Exception('No auth token');
      final response = await _ssoApi.getIncidentalInvoices(accessToken: token);
      if (response.statusCode == 200) {
        notifyListeners();
        return response.data;
      } else {
        _errorMessage = response.data.toString();
        notifyListeners();
        return null;
      }
    } catch (e) {
      _errorMessage = e.toString();
      notifyListeners();
      return null;
    } finally {
      setLoading(false);
    }
  }

  // SSO Register Member
  Future<bool> ssoRegisterMember(Map<String, dynamic> data) async {
    setLoading(true);
    try {
      final response = await _ssoApi.registerMember(data: data);
      if (response.statusCode == 200) {
        notifyListeners();
        return true;
      } else {
        _errorMessage = response.data.toString();
        notifyListeners();
        return false;
      }
    } catch (e) {
      _errorMessage = e.toString();
      notifyListeners();
      return false;
    } finally {
      setLoading(false);
    }
  }
}
