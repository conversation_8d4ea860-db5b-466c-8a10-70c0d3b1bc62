// lib/core/providers/user_search_provider.dart

import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../services/sso_api_service.dart'; // Assuming SsoApiService path

// 1. Define the State
class UserSearchState {
  final bool isLoading;
  final List<Map<String, dynamic>> results; // TODO: Replace Map with a proper model for type safety
  final String? error;

  UserSearchState({
    this.isLoading = false,
    this.results = const [],
    this.error,
  });

  UserSearchState copyWith({
    bool? isLoading,
    List<Map<String, dynamic>>? results,
    String? error,
    bool clearError = false, // To explicitly clear error
  }) {
    return UserSearchState(
      isLoading: isLoading ?? this.isLoading,
      results: results ?? this.results,
      error: clearError ? null : error ?? this.error,
    );
  }
}

// 2. Create the StateNotifier
class UserSearchNotifier extends StateNotifier<UserSearchState> {
  final SsoApiService _ssoApiService;

  UserSearchNotifier(this._ssoApiService) : super(UserSearchState());

  Future<void> searchUsers(String query) async {
    if (query.trim().isEmpty) {
      state = state.copyWith(results: [], isLoading: false, clearError: true);
      return;
    }
    state = state.copyWith(isLoading: true, clearError: true);
    try {
      final response = await _ssoApiService.searchUser(query: query);
      if (response.statusCode == 200 && response.data != null) {
        List<Map<String, dynamic>> userList = [];
        // Adapt this based on the actual structure of response.data
        if (response.data is List) {
            userList = List<Map<String, dynamic>>.from(response.data.map((item) => item as Map<String, dynamic>));
        } else if (response.data is Map<String, dynamic>) {
            // Common patterns: data might be under a 'users', 'data', or 'results' key
            var dataMap = response.data as Map<String, dynamic>;
            if (dataMap.containsKey('data') && dataMap['data'] is List) {
                userList = List<Map<String, dynamic>>.from(dataMap['data'].map((item) => item as Map<String, dynamic>));
            } else if (dataMap.containsKey('users') && dataMap['users'] is List) {
                userList = List<Map<String, dynamic>>.from(dataMap['users'].map((item) => item as Map<String, dynamic>));
            } else if (dataMap.containsKey('results') && dataMap['results'] is List) {
                userList = List<Map<String, dynamic>>.from(dataMap['results'].map((item) => item as Map<String, dynamic>));
            } else {
                // If the map itself is the user or if it's an unexpected structure
                // This part might need specific handling based on API contract
                print('User search API response format not recognized as a list container.');
            }
        }
        state = state.copyWith(isLoading: false, results: userList);
      } else {
        state = state.copyWith(
            isLoading: false, error: response.data?.toString() ?? 'Search failed (Status: ${response.statusCode})');
      }
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
    }
  }

  void clearSearch() {
    state = UserSearchState(); // Reset to initial state
  }
}

// 3. Create the Providers
// Provider for SsoApiService (assumes SsoApiService has a default constructor and no complex dependencies)
// If SsoApiService has dependencies or is already managed by Riverpod elsewhere, adjust this.
final ssoApiServiceProvider = Provider<SsoApiService>((ref) {
  // If SsoApiService needs Dio or other services, they should be read here:
  // final dio = ref.watch(dioProvider); // Example if Dio is also in Riverpod
  // return SsoApiService(dio);
  return SsoApiService(); // Assuming default constructor for now
});

final userSearchProvider = StateNotifierProvider<UserSearchNotifier, UserSearchState>((ref) {
  final ssoApiService = ref.watch(ssoApiServiceProvider);
  return UserSearchNotifier(ssoApiService);
});
