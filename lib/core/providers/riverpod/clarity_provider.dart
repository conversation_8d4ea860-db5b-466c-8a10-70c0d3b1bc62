import 'dart:developer';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../services/clarity_service.dart';

/// State class for Clarity analytics
class ClarityState {
  final bool isInitialized;
  final bool hasUserConsent;
  final bool isRecording;
  final String? sessionUrl;
  final String? userId;

  const ClarityState({
    this.isInitialized = false,
    this.hasUserConsent = false,
    this.isRecording = false,
    this.sessionUrl,
    this.userId,
  });

  ClarityState copyWith({
    bool? isInitialized,
    bool? hasUserConsent,
    bool? isRecording,
    String? sessionUrl,
    String? userId,
  }) {
    return ClarityState(
      isInitialized: isInitialized ?? this.isInitialized,
      hasUserConsent: hasUserConsent ?? this.hasUserConsent,
      isRecording: isRecording ?? this.isRecording,
      sessionUrl: sessionUrl ?? this.sessionUrl,
      userId: userId ?? this.userId,
    );
  }
}

/// Notifier for managing Clarity state
class ClarityNotifier extends StateNotifier<ClarityState> {
  static const String _consentKey = 'clarity_user_consent';
  static const String _userIdKey = 'clarity_user_id';

  ClarityNotifier() : super(const ClarityState()) {
    _loadSavedState();
  }

  /// Load saved consent and user preferences
  Future<void> _loadSavedState() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final hasConsent = prefs.getBool(_consentKey) ?? false;
      final savedUserId = prefs.getString(_userIdKey);

      state = state.copyWith(
        hasUserConsent: hasConsent,
        userId: savedUserId,
      );

      // If user previously gave consent, initialize Clarity
      if (hasConsent) {
        await _initializeClarity();
      }
    } catch (e) {
      log('Failed to load Clarity saved state: $e', name: 'ClarityProvider');
    }
  }

  /// Set user consent and save to preferences
  Future<void> setUserConsent(bool consent) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_consentKey, consent);

      state = state.copyWith(hasUserConsent: consent);
      ClarityService.setUserConsent(consent);

      if (consent) {
        await _initializeClarity();
      } else {
        await _stopClarity();
      }
    } catch (e) {
      log('Failed to set user consent: $e', name: 'ClarityProvider');
    }
  }

  /// Initialize Clarity service
  Future<void> _initializeClarity() async {
    try {
      await ClarityService.initialize();
      
      state = state.copyWith(isInitialized: ClarityService.isInitialized);

      if (state.isInitialized) {
        // Set user ID if available
        if (state.userId != null) {
          await ClarityService.setUserId(state.userId!);
        }

        // Start recording
        await startRecording();
      }
    } catch (e) {
      log('Failed to initialize Clarity: $e', name: 'ClarityProvider');
    }
  }

  /// Stop Clarity service
  Future<void> _stopClarity() async {
    try {
      await ClarityService.stopSessionRecording();
      state = state.copyWith(
        isRecording: false,
        sessionUrl: null,
      );
    } catch (e) {
      log('Failed to stop Clarity: $e', name: 'ClarityProvider');
    }
  }

  /// Start session recording
  Future<void> startRecording() async {
    if (!state.hasUserConsent || !state.isInitialized) {
      return;
    }

    try {
      await ClarityService.startSessionRecording();
      final sessionUrl = await ClarityService.getSessionUrl();
      
      state = state.copyWith(
        isRecording: true,
        sessionUrl: sessionUrl,
      );
    } catch (e) {
      log('Failed to start recording: $e', name: 'ClarityProvider');
    }
  }

  /// Stop session recording
  Future<void> stopRecording() async {
    try {
      await ClarityService.stopSessionRecording();
      state = state.copyWith(
        isRecording: false,
        sessionUrl: null,
      );
    } catch (e) {
      log('Failed to stop recording: $e', name: 'ClarityProvider');
    }
  }

  /// Set user ID for tracking
  Future<void> setUserId(String userId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_userIdKey, userId);

      state = state.copyWith(userId: userId);

      if (state.isInitialized) {
        await ClarityService.setUserId(userId);
      }
    } catch (e) {
      log('Failed to set user ID: $e', name: 'ClarityProvider');
    }
  }

  /// Clear user ID (on logout)
  Future<void> clearUserId() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_userIdKey);

      state = state.copyWith(userId: null);

      if (state.isInitialized) {
        await ClarityService.clearUserId();
      }
    } catch (e) {
      log('Failed to clear user ID: $e', name: 'ClarityProvider');
    }
  }

  /// Track custom event
  Future<void> trackEvent(String eventName, [Map<String, String>? properties]) async {
    if (!state.isInitialized || !state.hasUserConsent) {
      return;
    }

    try {
      await ClarityService.trackEvent(eventName, properties);
    } catch (e) {
      log('Failed to track event: $e', name: 'ClarityProvider');
    }
  }

  /// Set session property
  Future<void> setSessionProperty(String key, String value) async {
    if (!state.isInitialized || !state.hasUserConsent) {
      return;
    }

    try {
      await ClarityService.setSessionProperty(key, value);
    } catch (e) {
      log('Failed to set session property: $e', name: 'ClarityProvider');
    }
  }

  /// Get current session URL
  Future<String?> getSessionUrl() async {
    if (!state.isInitialized || !state.hasUserConsent) {
      return null;
    }

    try {
      final url = await ClarityService.getSessionUrl();
      state = state.copyWith(sessionUrl: url);
      return url;
    } catch (e) {
      log('Failed to get session URL: $e', name: 'ClarityProvider');
      return null;
    }
  }

  /// Reset all Clarity data (for privacy compliance)
  Future<void> resetAllData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_consentKey);
      await prefs.remove(_userIdKey);

      await _stopClarity();
      ClarityService.setUserConsent(false);

      state = const ClarityState();
    } catch (e) {
      log('Failed to reset Clarity data: $e', name: 'ClarityProvider');
    }
  }
}

/// Provider for Clarity state management
final clarityProvider = StateNotifierProvider<ClarityNotifier, ClarityState>((ref) {
  return ClarityNotifier();
});

/// Convenience providers for specific state values
final clarityConsentProvider = Provider<bool>((ref) {
  return ref.watch(clarityProvider).hasUserConsent;
});

final clarityInitializedProvider = Provider<bool>((ref) {
  return ref.watch(clarityProvider).isInitialized;
});

final clarityRecordingProvider = Provider<bool>((ref) {
  return ref.watch(clarityProvider).isRecording;
});

final claritySessionUrlProvider = Provider<String?>((ref) {
  return ref.watch(clarityProvider).sessionUrl;
});

final clarityUserIdProvider = Provider<String?>((ref) {
  return ref.watch(clarityProvider).userId;
});
