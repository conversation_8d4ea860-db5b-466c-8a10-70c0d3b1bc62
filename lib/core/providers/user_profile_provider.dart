// lib/core/providers/user_profile_provider.dart

import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../services/sso_api_service.dart'; // Assuming SsoApiService path
import './user_search_provider.dart'; // For ssoApiServiceProvider
import 'package:oneapp/core/constants.dart';
// TODO: Define a proper UserProfile model based on API response
// For now, using Map<String, dynamic>

// 1. Define the State
class UserProfileState {
  final bool isLoading;
  final Map<String, dynamic>? profileData;
  final String? error;

  UserProfileState({
    this.isLoading = false,
    this.profileData,
    this.error,
  });

  UserProfileState copyWith({
    bool? isLoading,
    Map<String, dynamic>? profileData,
    String? error,
    bool clearError = false,
    bool clearData = false,
  }) {
    return UserProfileState(
      isLoading: isLoading ?? this.isLoading,
      profileData: clearData ? null : profileData ?? this.profileData,
      error: clearError ? null : error ?? this.error,
    );
  }
}

// 2. Create the StateNotifier
class UserProfileNotifier extends StateNotifier<UserProfileState> {
  final SsoApiService _ssoApiService;

  UserProfileNotifier(this._ssoApiService) : super(UserProfileState());

  Future<void> fetchUserProfile(String accessToken) async {
    if (accessToken.isEmpty) {
      state = state.copyWith(error: 'Access token is missing.', isLoading: false);
      return;
    }
    state = state.copyWith(isLoading: true, clearError: true, clearData: true);
    try {
      final response = await _ssoApiService.getProfile(accessToken);
      if (response.statusCode == 200 && response.data != null) {
        // Assuming response.data is the profile map
        state = state.copyWith(isLoading: false, profileData: response.data as Map<String, dynamic>);
      } else {
        state = state.copyWith(
            isLoading: false, error: response.data?.toString() ?? 'Failed to fetch profile (Status: ${response.statusCode})');
      }
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
    }
  }

  void clearProfile() {
    state = UserProfileState(); // Reset to initial state
  }
}

// 3. Create the Providers
// Assuming ssoApiServiceProvider is already defined (e.g., in user_search_provider.dart or a dedicated api_service_provider.dart)
// If not, you'd define it here similar to how it was done for user_search_provider.
// final ssoApiServiceProvider = Provider<SsoApiService>((ref) => SsoApiService());

final userProfileProvider = StateNotifierProvider<UserProfileNotifier, UserProfileState>((ref) {
  final ssoApiService = ref.watch(ssoApiServiceProvider); // Assumes ssoApiServiceProvider exists
  return UserProfileNotifier(ssoApiService);
});