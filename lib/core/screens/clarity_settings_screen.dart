import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../providers/riverpod/clarity_provider.dart';
import '../widgets/clarity_consent_dialog.dart';
import '../theme/app_theme.dart';

/// Settings screen for managing Microsoft Clarity analytics preferences
class ClaritySettingsScreen extends ConsumerWidget {
  const ClaritySettingsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final clarityState = ref.watch(clarityProvider);
    final clarityNotifier = ref.read(clarityProvider.notifier);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Analytics Settings'),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        elevation: 0,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(),
            const SizedBox(height: 24),
            _buildConsentSection(context, ref, clarityState, clarityNotifier),
            const SizedBox(height: 24),
            _buildStatusSection(clarityState),
            const SizedBox(height: 24),
            _buildSessionInfoSection(clarityState),
            const SizedBox(height: 24),
            _buildPrivacySection(context, clarityNotifier),
            const SizedBox(height: 24),
            _buildAboutSection(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppTheme.primaryColor.withOpacity(0.1),
            AppTheme.primaryColor.withOpacity(0.05),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppTheme.primaryColor.withOpacity(0.2),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.analytics_outlined,
            color: AppTheme.primaryColor,
            size: 32,
          ),
          const SizedBox(width: 16),
          const Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Microsoft Clarity',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: 4),
                Text(
                  'User behavior analytics and session recording',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildConsentSection(
    BuildContext context,
    WidgetRef ref,
    ClarityState clarityState,
    ClarityNotifier clarityNotifier,
  ) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Analytics Consent',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: Text(
                    clarityState.hasUserConsent
                        ? 'Analytics enabled - helping us improve your experience'
                        : 'Analytics disabled - no data is being collected',
                    style: TextStyle(
                      color: clarityState.hasUserConsent ? Colors.green : Colors.grey,
                    ),
                  ),
                ),
                Switch(
                  value: clarityState.hasUserConsent,
                  onChanged: (value) async {
                    if (value) {
                      // Show consent dialog when enabling
                      final consent = await ClarityConsentDialog.show(context);
                      if (consent == true) {
                        await clarityNotifier.setUserConsent(true);
                      }
                    } else {
                      // Disable directly
                      await clarityNotifier.setUserConsent(false);
                    }
                  },
                  activeColor: AppTheme.primaryColor,
                ),
              ],
            ),
            if (!clarityState.hasUserConsent) ...[
              const SizedBox(height: 12),
              ElevatedButton.icon(
                onPressed: () async {
                  final consent = await ClarityConsentDialog.show(context);
                  if (consent == true) {
                    await clarityNotifier.setUserConsent(true);
                  }
                },
                icon: const Icon(Icons.info_outline),
                label: const Text('Learn More & Enable'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.primaryColor,
                  foregroundColor: Colors.white,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildStatusSection(ClarityState clarityState) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Status',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            _buildStatusItem(
              'Initialized',
              clarityState.isInitialized,
              Icons.check_circle,
              Icons.cancel,
            ),
            _buildStatusItem(
              'Recording',
              clarityState.isRecording,
              Icons.fiber_manual_record,
              Icons.stop_circle,
            ),
            _buildStatusItem(
              'User Consent',
              clarityState.hasUserConsent,
              Icons.verified_user,
              Icons.block,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusItem(String label, bool status, IconData activeIcon, IconData inactiveIcon) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Icon(
            status ? activeIcon : inactiveIcon,
            color: status ? Colors.green : Colors.grey,
            size: 20,
          ),
          const SizedBox(width: 12),
          Text(label),
          const Spacer(),
          Text(
            status ? 'Active' : 'Inactive',
            style: TextStyle(
              color: status ? Colors.green : Colors.grey,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSessionInfoSection(ClarityState clarityState) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Session Information',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            if (clarityState.userId != null) ...[
              _buildInfoRow('User ID', clarityState.userId!),
              const SizedBox(height: 8),
            ],
            if (clarityState.sessionUrl != null) ...[
              _buildInfoRow('Session URL', clarityState.sessionUrl!),
              const SizedBox(height: 8),
            ],
            if (clarityState.userId == null && clarityState.sessionUrl == null)
              const Text(
                'No session information available',
                style: TextStyle(color: Colors.grey),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 100,
          child: Text(
            '$label:',
            style: const TextStyle(fontWeight: FontWeight.w500),
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: const TextStyle(color: Colors.grey),
          ),
        ),
      ],
    );
  }

  Widget _buildPrivacySection(BuildContext context, ClarityNotifier clarityNotifier) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Privacy Controls',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            ListTile(
              leading: const Icon(Icons.delete_forever, color: Colors.red),
              title: const Text('Reset All Data'),
              subtitle: const Text('Clear all analytics data and preferences'),
              onTap: () => _showResetConfirmation(context, clarityNotifier),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAboutSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'About Microsoft Clarity',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            const Text(
              'Microsoft Clarity is a free user behavior analytics tool that helps us understand how you interact with our app. It provides insights through session recordings and heatmaps while respecting your privacy.',
              style: TextStyle(color: Colors.grey),
            ),
            const SizedBox(height: 12),
            TextButton.icon(
              onPressed: () {
                // Open privacy policy or Clarity documentation
              },
              icon: const Icon(Icons.open_in_new),
              label: const Text('Learn More'),
            ),
          ],
        ),
      ),
    );
  }

  void _showResetConfirmation(BuildContext context, ClarityNotifier clarityNotifier) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Reset Analytics Data'),
        content: const Text(
          'This will clear all analytics preferences and stop data collection. You can re-enable analytics later.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              await clarityNotifier.resetAllData();
              if (context.mounted) {
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Analytics data reset successfully'),
                    backgroundColor: Colors.green,
                  ),
                );
              }
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Reset', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }
}
