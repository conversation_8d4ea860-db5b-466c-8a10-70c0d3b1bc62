import 'package:flutter/material.dart';
import 'base_view.dart';

/// Base stateless widget that implements BaseView
abstract class BaseStatelessView extends StatelessWidget implements BaseView {
  const BaseStatelessView({Key? key}) : super(key: key);
  
  @override
  void showLoading() {
    // Implement loading indicator
  }
  
  @override
  void hideLoading() {
    // Hide loading indicator
  }
  
  @override
  void showError(String message, BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: Colors.red),
    );
  }
  
  @override
  void showSuccess(String message, BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: Colors.green),
    );
  }
  
  @override
  void showDialog(String title, String message, BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
}
