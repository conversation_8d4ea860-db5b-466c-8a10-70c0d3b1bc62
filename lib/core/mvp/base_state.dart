import 'package:flutter/material.dart';
import 'base_presenter.dart';
import 'base_view.dart';

/// Base state for stateful widgets that use MVP pattern
abstract class BaseState<T extends StatefulWidget, P extends BasePresenter> 
    extends State<T> implements BaseView {
  /// The presenter instance
  late final P presenter;
  
  /// Creates the presenter instance
  P createPresenter();
  
  @override
  void initState() {
    super.initState();
    presenter = createPresenter();
    presenter.attachView(this as dynamic);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        presenter.onViewInitialized(context);
      }
    });
  }
  
  @override
  void dispose() {
    presenter.dispose();
    presenter.detachView();
    super.dispose();
  }
  
  @override
  void showLoading() {
    // Implement loading indicator
  }
  
  @override
  void hideLoading() {
    // Hide loading indicator
  }
  
  @override
  void showError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: Colors.red),
    );
  }
  
  @override
  void showSuccess(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: Colors.green),
    );
  }
  
  @override
  void showDialog(String title, String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
}
