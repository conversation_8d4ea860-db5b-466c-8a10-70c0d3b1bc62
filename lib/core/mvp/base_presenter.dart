import 'package:flutter/material.dart';

/// Base presenter interface that all presenters should implement
abstract class BasePresenter<T> {
  /// Called when the view is initialized
  void onViewInitialized(BuildContext context);
  
  /// Called when the view is disposed
  void dispose();
  
  /// Reference to the view interface
  T? _view;
  
  /// Attaches the view to the presenter
  void attachView(T view) {
    _view = view;
  }
  
  /// Detaches the view from the presenter
  void detachView() {
    _view = null;
  }
  
  /// Returns true if the view is attached
  bool get isViewAttached => _view != null;
  
  /// Returns the attached view
  T? get view => _view;
  
  /// Asserts that the view is attached
  void checkViewAttached() {
    if (_view == null) {
      throw Exception('View is not attached');
    }
  }
}
