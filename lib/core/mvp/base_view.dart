/// Base view interface that all views should implement
abstract class BaseView {
  /// Shows a loading indicator
  void showLoading();
  
  /// Hides the loading indicator
  void hideLoading();
  
  /// Shows an error message
  void showError(String message);
  
  /// Shows a success message
  void showSuccess(String message);
  
  /// Shows a dialog with the given message
  void showDialog(String title, String message);
}
