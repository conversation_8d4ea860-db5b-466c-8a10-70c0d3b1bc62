import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../providers/riverpod/clarity_provider.dart';
import '../widgets/clarity_consent_dialog.dart';

/// Widget that handles Clarity integration and consent flow
/// Place this widget in your app's main widget tree to enable Clarity
class ClarityIntegrationWidget extends ConsumerStatefulWidget {
  final Widget child;
  final bool showConsentOnFirstLaunch;
  final Duration delayBeforeConsent;

  const ClarityIntegrationWidget({
    super.key,
    required this.child,
    this.showConsentOnFirstLaunch = true,
    this.delayBeforeConsent = const Duration(seconds: 2),
  });

  @override
  ConsumerState<ClarityIntegrationWidget> createState() => _ClarityIntegrationWidgetState();
}

class _ClarityIntegrationWidgetState extends ConsumerState<ClarityIntegrationWidget> {
  bool _hasShownConsentDialog = false;

  @override
  void initState() {
    super.initState();
    
    // Schedule consent dialog if needed
    if (widget.showConsentOnFirstLaunch) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _scheduleConsentDialog();
      });
    }
  }

  void _scheduleConsentDialog() {
    Future.delayed(widget.delayBeforeConsent, () {
      if (mounted && !_hasShownConsentDialog) {
        _checkAndShowConsentDialog();
      }
    });
  }

  Future<void> _checkAndShowConsentDialog() async {
    final clarityState = ref.read(clarityProvider);
    
    // Only show consent dialog if:
    // 1. User hasn't given consent yet
    // 2. We haven't shown the dialog in this session
    // 3. Widget is still mounted
    if (!clarityState.hasUserConsent && !_hasShownConsentDialog && mounted) {
      _hasShownConsentDialog = true;
      
      final consent = await ClarityConsentDialog.show(context);
      if (consent == true && mounted) {
        await ref.read(clarityProvider.notifier).setUserConsent(true);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // Listen to Clarity state for debugging purposes
    ref.listen<ClarityState>(clarityProvider, (previous, next) {
      // Track important state changes
      if (previous?.isInitialized != next.isInitialized && next.isInitialized) {
        debugPrint('Clarity: Successfully initialized');
      }
      
      if (previous?.isRecording != next.isRecording && next.isRecording) {
        debugPrint('Clarity: Session recording started');
      }
      
      if (previous?.hasUserConsent != next.hasUserConsent) {
        debugPrint('Clarity: User consent changed to ${next.hasUserConsent}');
      }
    });

    return widget.child;
  }
}

/// Mixin for screens that want to track Clarity events
mixin ClarityTrackingMixin<T extends ConsumerStatefulWidget> on ConsumerState<T> {
  
  /// Track screen view
  void trackScreenView(String screenName) {
    final clarityNotifier = ref.read(clarityProvider.notifier);
    clarityNotifier.trackEvent('screen_view', {
      'screen_name': screenName,
      'timestamp': DateTime.now().toIso8601String(),
    });
  }

  /// Track user action
  void trackUserAction(String action, {Map<String, String>? properties}) {
    final clarityNotifier = ref.read(clarityProvider.notifier);
    final eventProperties = {
      'action': action,
      'timestamp': DateTime.now().toIso8601String(),
      ...?properties,
    };
    clarityNotifier.trackEvent('user_action', eventProperties);
  }

  /// Track feature usage
  void trackFeatureUsage(String feature, {Map<String, String>? properties}) {
    final clarityNotifier = ref.read(clarityProvider.notifier);
    final eventProperties = {
      'feature': feature,
      'timestamp': DateTime.now().toIso8601String(),
      ...?properties,
    };
    clarityNotifier.trackEvent('feature_usage', eventProperties);
  }

  /// Track error or exception
  void trackError(String error, {String? context, Map<String, String>? properties}) {
    final clarityNotifier = ref.read(clarityProvider.notifier);
    final eventProperties = {
      'error': error,
      'timestamp': DateTime.now().toIso8601String(),
      if (context != null) 'context': context,
      ...?properties,
    };
    clarityNotifier.trackEvent('error', eventProperties);
  }

  /// Set user properties when user logs in
  void setClarityUserContext(String userId, {Map<String, String>? userProperties}) {
    final clarityNotifier = ref.read(clarityProvider.notifier);
    
    // Set user ID
    clarityNotifier.setUserId(userId);
    
    // Set user properties
    if (userProperties != null) {
      for (final entry in userProperties.entries) {
        clarityNotifier.setSessionProperty('user_${entry.key}', entry.value);
      }
    }
    
    // Track login event
    trackUserAction('user_login', {'user_id': userId});
  }

  /// Clear user context when user logs out
  void clearClarityUserContext() {
    final clarityNotifier = ref.read(clarityProvider.notifier);
    
    // Track logout event before clearing
    trackUserAction('user_logout');
    
    // Clear user ID
    clarityNotifier.clearUserId();
  }
}

/// Extension for easy Clarity tracking in any widget
extension ClarityTracking on WidgetRef {
  
  /// Quick access to track events
  void trackClarityEvent(String eventName, [Map<String, String>? properties]) {
    read(clarityProvider.notifier).trackEvent(eventName, properties);
  }

  /// Quick access to set session properties
  void setClarityProperty(String key, String value) {
    read(clarityProvider.notifier).setSessionProperty(key, value);
  }

  /// Check if Clarity is recording
  bool get isClarityRecording => read(clarityProvider).isRecording;

  /// Check if user has given consent
  bool get hasClarityConsent => read(clarityProvider).hasUserConsent;
}

/// Helper class for common Clarity tracking scenarios
class ClarityTracker {
  static void trackAppLaunch(WidgetRef ref) {
    ref.trackClarityEvent('app_launch', {
      'timestamp': DateTime.now().toIso8601String(),
      'platform': Theme.of(ref.context).platform.name,
    });
  }

  static void trackAppBackground(WidgetRef ref) {
    ref.trackClarityEvent('app_background', {
      'timestamp': DateTime.now().toIso8601String(),
    });
  }

  static void trackAppForeground(WidgetRef ref) {
    ref.trackClarityEvent('app_foreground', {
      'timestamp': DateTime.now().toIso8601String(),
    });
  }

  static void trackNavigation(WidgetRef ref, String from, String to) {
    ref.trackClarityEvent('navigation', {
      'from': from,
      'to': to,
      'timestamp': DateTime.now().toIso8601String(),
    });
  }

  static void trackButtonTap(WidgetRef ref, String buttonName, {String? context}) {
    ref.trackClarityEvent('button_tap', {
      'button_name': buttonName,
      if (context != null) 'context': context,
      'timestamp': DateTime.now().toIso8601String(),
    });
  }

  static void trackFormSubmission(WidgetRef ref, String formName, {bool? success}) {
    ref.trackClarityEvent('form_submission', {
      'form_name': formName,
      if (success != null) 'success': success.toString(),
      'timestamp': DateTime.now().toIso8601String(),
    });
  }

  static void trackSearchQuery(WidgetRef ref, String query, {int? resultCount}) {
    ref.trackClarityEvent('search', {
      'query': query,
      if (resultCount != null) 'result_count': resultCount.toString(),
      'timestamp': DateTime.now().toIso8601String(),
    });
  }
}
