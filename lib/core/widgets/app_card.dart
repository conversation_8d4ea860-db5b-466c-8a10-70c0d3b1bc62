import 'package:flutter/material.dart';
import '../theme/theme_constants.dart';
import '../theme/colors.dart';

class AppCard extends StatelessWidget {
  final Widget child;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final double? elevation;
  final Color? backgroundColor;
  final BorderRadius? borderRadius;
  final bool useGradient;
  final Gradient? gradient;
  final Function()? onTap;
  final double? height;
  final double? width;

  const AppCard({
    Key? key,
    required this.child,
    this.padding,
    this.margin,
    this.elevation,
    this.backgroundColor,
    this.borderRadius,
    this.useGradient = false,
    this.gradient,
    this.onTap,
    this.height,
    this.width,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final isTablet = constraints.maxWidth > ThemeConstants.mobileBreakpoint;
        final isDesktop =
            constraints.maxWidth > ThemeConstants.tabletBreakpoint;

        // Responsive values
        final defaultPadding = isDesktop
            ? const EdgeInsets.all(24.0)
            : (isTablet
                ? const EdgeInsets.all(16.0)
                : const EdgeInsets.all(12.0));

        final defaultMargin = isDesktop
            ? const EdgeInsets.all(16.0)
            : (isTablet
                ? const EdgeInsets.all(12.0)
                : const EdgeInsets.all(8.0));

        final defaultRadius = isDesktop ? 16.0 : (isTablet ? 12.0 : 8.0);
        final defaultElevation = isDesktop ? 4.0 : (isTablet ? 3.0 : 2.0);

        return GestureDetector(
          onTap: onTap,
          child: Container(
            height: height,
            width: width,
            margin: margin ?? defaultMargin,
            decoration: BoxDecoration(
              color: useGradient
                  ? null
                  : (backgroundColor ?? ThemeConstants.cardBackground),
              gradient: useGradient
                  ? (gradient ??
                      LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: [
                          ThemeConstants.primary,
                          ThemeConstants.primary.withOpacity(0.7),
                        ],
                      ))
                  : null,
              borderRadius: borderRadius ?? BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: AppColors.coolGrey.withOpacity(0.1),
                  blurRadius: elevation ?? defaultElevation,
                  spreadRadius: 0,
                  offset: Offset(0, (elevation ?? defaultElevation) / 2),
                ),
              ],
            ),
            child: Padding(padding: padding ?? defaultPadding, child: child),
          ),
        );
      },
    );
  }
}

class AppGridCard extends StatelessWidget {
  final Widget child;
  final String title;
  final IconData icon;
  final VoidCallback onTap;
  final Color? backgroundColor;
  final Color? iconColor;

  const AppGridCard({
    Key? key,
    required this.child,
    required this.title,
    required this.icon,
    required this.onTap,
    this.backgroundColor,
    this.iconColor,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final isTablet = constraints.maxWidth > ThemeConstants.mobileBreakpoint;
        final screenWidth = MediaQuery.of(context).size.width;
        final cardWidth =
            screenWidth / (isTablet ? 3 : 2) - (isTablet ? 24 : 16);

        return AppCard(
          width: cardWidth,
          backgroundColor: backgroundColor ?? Colors.white,
          onTap: onTap,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Icon(
                icon,
                color: iconColor ?? ThemeConstants.primary,
                size: isTablet ? 40 : 32,
              ),
              SizedBox(height: isTablet ? 16 : 8),
              Text(
                title,
                style: TextStyle(
                  fontSize: isTablet ? 16 : 14,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: isTablet ? 12 : 8),
              Flexible(child: child),
            ],
          ),
        );
      },
    );
  }
}

class ResponsiveGridView extends StatelessWidget {
  final List<Widget> children;
  final double spacing;
  final double runSpacing;
  final EdgeInsetsGeometry? padding;

  const ResponsiveGridView({
    Key? key,
    required this.children,
    this.spacing = 16.0,
    this.runSpacing = 16.0,
    this.padding,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final screenWidth = constraints.maxWidth;
        int crossAxisCount;

        if (screenWidth < 360) {
          crossAxisCount = 1; // Extra small screens
        } else if (screenWidth < ThemeConstants.mobileBreakpoint) {
          crossAxisCount = 2; // Mobile phones
        } else if (screenWidth < ThemeConstants.tabletBreakpoint) {
          crossAxisCount = 3; // Tablets
        } else {
          crossAxisCount = 4; // Large tablets/desktops
        }

        return Padding(
          padding: padding ?? EdgeInsets.zero,
          child: GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: crossAxisCount,
              childAspectRatio: 1.0,
              crossAxisSpacing: spacing,
              mainAxisSpacing: runSpacing,
            ),
            itemCount: children.length,
            itemBuilder: (context, index) => children[index],
          ),
        );
      },
    );
  }
}
