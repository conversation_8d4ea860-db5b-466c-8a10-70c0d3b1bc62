import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:oneapp/core/theme/colors.dart';

import '../models/company_model.dart';
import '../providers/riverpod/company_provider.dart';

class CompanySelector extends ConsumerStatefulWidget {
  const CompanySelector({Key? key}) : super(key: key);

  @override
  ConsumerState<CompanySelector> createState() => _CompanySelectorState();
}

class _CompanySelectorState extends ConsumerState<CompanySelector> {
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  List<CompanyModel> _getFilteredCompanies(List<CompanyModel> companies) {
    if (_searchQuery.isEmpty) {
      return companies;
    }
    return companies
        .where((company) => company.companyName
            .toLowerCase()
            .contains(_searchQuery.toLowerCase()))
        .toList();
  }

  @override
  Widget build(BuildContext context) {
    final companyState = ref.watch(companyProvider);

    if (companyState.isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (companyState.errorMessage != null) {
      return Center(
        child: Text(
          'Error: ${companyState.errorMessage}',
          style: const TextStyle(color: Colors.red),
        ),
      );
    }

    if (companyState.companies.isEmpty) {
      return const Center(
        child: Text('No companies available'),
      );
    }

    final filteredCompanies = _getFilteredCompanies(companyState.companies);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Select/Search Society',
            style: Theme.of(context).textTheme.titleLarge,
          ),
              const Spacer(),
              Container(
                decoration: BoxDecoration(
                  color: Colors.grey.shade100,
                  shape: BoxShape.circle,
                ),
                child: IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: () => Navigator.pop(context),
                  color: Colors.red,
                ),
              ),
            ],
          ),
        ),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'Search society...',
              prefixIcon: const Icon(Icons.search),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: Colors.grey.withOpacity(0.3)),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: Colors.grey.withOpacity(0.3)),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: const BorderSide(color: AppColors.primary),
              ),
              contentPadding:
                  const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              filled: true,
              fillColor: Colors.grey.withOpacity(0.1),
            ),
            onChanged: (value) {
              setState(() {
                _searchQuery = value;
              });
            },
          ),
        ),
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 8),
          child: Divider(
            height: 1,
            thickness: 1,
            color: Colors.grey.withOpacity(0.2),
          ),
        ),
        if (filteredCompanies.isEmpty)
          Expanded(
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.search_off_rounded,
                    size: 64,
                    color: Colors.grey.withOpacity(0.5),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'No matching society found',
                    style: TextStyle(
                      color: Colors.grey.shade600,
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  if (_searchQuery.isNotEmpty)
                    Padding(
                      padding: const EdgeInsets.only(top: 8),
                      child: Text(
                        'Try a different search term',
                        style: TextStyle(
                          color: Colors.grey.shade500,
                          fontSize: 14,
                        ),
                      ),
                    ),
                ],
              ),
            ),
          )
        else
        Expanded(
          child: ListView.builder(
              itemCount: filteredCompanies.length,
            itemBuilder: (context, index) {
                final company = filteredCompanies[index];
                final isSelected = companyState.selectedCompany?.companyId ==
                    company.companyId;

              return ListTile(
                title: Text(
                  company.companyName,
                  style: TextStyle(
                    fontWeight:
                        isSelected ? FontWeight.bold : FontWeight.normal,
                  ),
                ),
                subtitle: Text(
                  'Roles: ${company.userRoles.join(", ")}',
                  style: const TextStyle(fontSize: 12),
                ),
                leading: CircleAvatar(
                  backgroundColor:
                      isSelected ? AppColors.primary : Colors.grey.shade200,
                  child: Icon(
                    Icons.business_rounded,
                    color: isSelected ? Colors.white : Colors.grey,
                  ),
                ),
                trailing: isSelected
                    ? const Icon(
                        Icons.check_circle,
                        color: AppColors.primary,
                      )
                    : null,
                onTap: () {
                  ref.read(companyProvider.notifier).selectCompany(company);
                  Navigator.pop(context);
                },
              );
            },
          ),
        ),
      ],
    );
  }
}

// A button that opens the company selector
class CompanySelectorButton extends ConsumerWidget {
  const CompanySelectorButton({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final companyState = ref.watch(companyProvider);
    final selectedCompany = companyState.selectedCompany;

    return InkWell(
      onTap: () {
        showModalBottomSheet(
          context: context,
          isScrollControlled: true,
          shape: const RoundedRectangleBorder(
            borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
          ),
          builder: (context) => SizedBox(
            height: MediaQuery.of(context).size.height * 0.7,
            child: const CompanySelector(),
          ),
        );
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: AppColors.primary.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(
              Icons.business_rounded,
              size: 16,
              color: AppColors.primary,
            ),
            const SizedBox(width: 8),
            Flexible(
              child: Text(
                selectedCompany?.companyName ?? 'Select Company',
                style: const TextStyle(
                  color: AppColors.primary,
                  fontWeight: FontWeight.bold,
                ),
                overflow: TextOverflow.ellipsis,
              ),
            ),
            const SizedBox(width: 4),
            const Icon(
              Icons.arrow_drop_down,
              size: 16,
              color: AppColors.primary,
            ),
          ],
        ),
      ),
    );
  }
}
