import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../providers/riverpod/clarity_provider.dart';

/// Simplified Clarity integration widget
/// This widget handles the basic consent flow for Microsoft Clarity
class SimpleClarityIntegration extends ConsumerStatefulWidget {
  final Widget child;
  final bool showConsentOnFirstLaunch;
  final Duration delayBeforeConsent;

  const SimpleClarityIntegration({
    super.key,
    required this.child,
    this.showConsentOnFirstLaunch = true,
    this.delayBeforeConsent = const Duration(seconds: 3),
  });

  @override
  ConsumerState<SimpleClarityIntegration> createState() => _SimpleClarityIntegrationState();
}

class _SimpleClarityIntegrationState extends ConsumerState<SimpleClarityIntegration> {
  bool _hasShownConsentDialog = false;

  @override
  void initState() {
    super.initState();
    
    // Schedule consent dialog if needed
    if (widget.showConsentOnFirstLaunch) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _scheduleConsentDialog();
      });
    }
  }

  void _scheduleConsentDialog() {
    Future.delayed(widget.delayBeforeConsent, () {
      if (mounted && !_hasShownConsentDialog) {
        _checkAndShowConsentDialog();
      }
    });
  }

  Future<void> _checkAndShowConsentDialog() async {
    final clarityState = ref.read(clarityProvider);

    // Only show consent dialog if user hasn't given consent yet
    if (!clarityState.hasUserConsent && !_hasShownConsentDialog && mounted) {
      _hasShownConsentDialog = true;

      // For now, show a simple dialog instead of the complex consent dialog
      final consent = await _showSimpleConsentDialog();
      if (consent == true && mounted) {
        await ref.read(clarityProvider.notifier).setUserConsent(true);
      }
    }
  }

  Future<bool?> _showSimpleConsentDialog() async {
    return showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Text('Analytics Consent'),
        content: const Text(
          'This app uses Microsoft Clarity to improve user experience. '
          'Would you like to enable analytics?'
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Decline'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('Accept'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    // Listen to Clarity state for debugging purposes
    ref.listen<ClarityState>(clarityProvider, (previous, next) {
      // Track important state changes
      if (previous?.isInitialized != next.isInitialized && next.isInitialized) {
        debugPrint('Clarity: Successfully initialized');
      }
      
      if (previous?.isRecording != next.isRecording && next.isRecording) {
        debugPrint('Clarity: Session recording started');
      }
      
      if (previous?.hasUserConsent != next.hasUserConsent) {
        debugPrint('Clarity: User consent changed to ${next.hasUserConsent}');
      }
    });

    return widget.child;
  }
}

/// Simple helper class for basic Clarity tracking
class SimpleClarityTracker {
  static void trackAppLaunch(WidgetRef ref) {
    final clarityNotifier = ref.read(clarityProvider.notifier);
    clarityNotifier.trackEvent('app_launch');
  }

  static void trackScreenView(WidgetRef ref, String screenName) {
    final clarityNotifier = ref.read(clarityProvider.notifier);
    clarityNotifier.trackEvent('screen_view');
  }

  static void trackButtonTap(WidgetRef ref, String buttonName) {
    final clarityNotifier = ref.read(clarityProvider.notifier);
    clarityNotifier.trackEvent('button_tap');
  }

  static void trackUserLogin(WidgetRef ref, String userId) {
    final clarityNotifier = ref.read(clarityProvider.notifier);
    clarityNotifier.setUserId(userId);
    clarityNotifier.trackEvent('user_login');
  }

  static void trackUserLogout(WidgetRef ref) {
    final clarityNotifier = ref.read(clarityProvider.notifier);
    clarityNotifier.trackEvent('user_logout');
    clarityNotifier.clearUserId();
  }
}
