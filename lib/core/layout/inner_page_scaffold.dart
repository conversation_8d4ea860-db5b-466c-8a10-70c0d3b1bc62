import 'package:flutter/material.dart';

import '../theme/app_theme.dart';
import '../theme/colors.dart';

class InnerPageScaffold extends StatelessWidget {
  final String title;
  final IconData? icon;
  final Widget body;
  final List<Widget>? actions;
  final Widget? floatingActionButton;
  final bool useGradientAppBar;
  final Widget? headerWidget;
  final Color? backgroundColor;
  final bool hasScrollBody;
  final bool extendBodyBehindAppBar;

  const InnerPageScaffold({
    Key? key,
    required this.title,
    this.icon,
    required this.body,
    this.actions,
    this.floatingActionButton,
    this.useGradientAppBar = false,
    this.headerWidget,
    this.backgroundColor,
    this.hasScrollBody = true,
    this.extendBodyBehindAppBar = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: backgroundColor ?? AppTheme.backgroundColor,
      extendBodyBehindAppBar: extendBodyBehindAppBar,
      appBar: _buildAppBar(context),
      body: Safe<PERSON>rea(
        child: hasScrollBody
            ? SingleChildScrollView(
                physics: const BouncingScrollPhysics(),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    if (headerWidget != null) headerWidget!,
                    body,
                  ],
                ),
              )
            : Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (headerWidget != null) headerWidget!,
                  Expanded(child: body),
                ],
              ),
      ),
      floatingActionButton: floatingActionButton,
    );
  }

  PreferredSizeWidget _buildAppBar(BuildContext context) {
    return AppBar(
      elevation: 0,
      scrolledUnderElevation: 2,
      backgroundColor:
          useGradientAppBar ? Colors.transparent : AppTheme.backgroundColor,
      flexibleSpace: useGradientAppBar
          ? Container(
              decoration: BoxDecoration(
                gradient: AppColors.redToGreyGradient,
              ),
            )
          : null,
      iconTheme: IconThemeData(
        color: useGradientAppBar ? Colors.white : AppColors.textPrimary,
      ),
      centerTitle: false,
      leading: IconButton(
        icon: const Icon(Icons.arrow_back_rounded),
        onPressed: () {
          // Simple back navigation - no GoRouter
          Navigator.of(context, rootNavigator: false).pop();
        },
      ),
      title: Text(
        title,
        style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: useGradientAppBar ? Colors.white : null,
            ),
        textAlign: TextAlign.left,
      ),
      actions: actions,
    );
  }
}
