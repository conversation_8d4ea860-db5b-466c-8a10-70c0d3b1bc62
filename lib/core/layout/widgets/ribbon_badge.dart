import 'package:flutter/material.dart';
import 'package:badges/badges.dart' as badges;

import '../../theme/colors.dart';

class RibbonBadge extends StatelessWidget {
  final String text;
  final Color? startColor;
  final Color? endColor;
  final double? badgeWidth;
  final double? badgeHeight;
  final bool rotated;

  const RibbonBadge({
    Key? key,
    required this.text,
    this.startColor,
    this.endColor,
    this.badgeWidth,
    this.badgeHeight,
    this.rotated = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Positioned(
      top: 0,
      right: 0,
      child: badges.Badge(
        badgeAnimation: const badges.BadgeAnimation.rotation(
          animationDuration: Duration(milliseconds: 0),
          colorChangeAnimationDuration: Duration(milliseconds: 300),
        ),
        badgeStyle: badges.BadgeStyle(
          shape: badges.BadgeShape.square,
          borderRadius: const BorderRadius.only(
            topRight: Radius.circular(16),
            bottomLeft: Radius.circular(8),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
          elevation: 0,
          badgeGradient: badges.BadgeGradient.linear(
            colors: [
              startColor ?? AppColors.warning,
              endColor ?? AppColors.primaryLight,
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        badgeContent: Text(
          text.toUpperCase(),
          style: const TextStyle(
            color: Colors.white,
            fontSize: 10,
            fontWeight: FontWeight.bold,
            letterSpacing: 0.5,
          ),
        ),
      ),
    );
  }
}
