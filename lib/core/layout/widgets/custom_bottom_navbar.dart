import 'package:flutter/material.dart';
import '../../theme/colors.dart';
import '../../theme/app_theme.dart';
import '../../theme/theme_constants.dart';

class CustomBottomNavBar extends StatelessWidget {
  final int currentIndex;
  final Function(int) onTap;

  const CustomBottomNavBar({
    Key? key,
    required this.currentIndex,
    required this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final screenWidth = constraints.maxWidth;
        final isSmallScreen = screenWidth < 360; // For very small screens
        final isTablet = screenWidth > ThemeConstants.mobileBreakpoint;

        final horizontalPadding = isSmallScreen ? 4.0 : (isTablet ? 16.0 : 8.0);
        final verticalPadding = isTablet ? 12.0 : 8.0;

        return Container(
          decoration: BoxDecoration(
            color: Colors.white,
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.05),
                blurRadius: 10,
                spreadRadius: 0,
                offset: const Offset(0, -5),
              ),
            ],
          ),
          child: SafeArea(
            top: false,
            child: Padding(
              padding: EdgeInsets.symmetric(
                horizontal: horizontalPadding,
                vertical: verticalPadding,
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  _buildNavItem(
                    context: context,
                    icon: Icons.home_rounded,
                    label: 'Household',
                    index: 0,
                    isSmallScreen: isSmallScreen,
                    isTablet: isTablet,
                  ),
                  _buildNavItem(
                    context: context,
                    icon: Icons.payment_rounded,
                    label: 'Payments',
                    index: 1,
                    isSmallScreen: isSmallScreen,
                    isTablet: isTablet,
                  ),
                  _buildNavItem(
                    context: context,
                    icon: Icons.work_rounded,
                    label: isSmallScreen ? 'Work' : 'Work',
                    index: 2,
                    isSmallScreen: isSmallScreen,
                    isTablet: isTablet,
                  ),
                  _buildNavItem(
                    context: context,
                    icon: Icons.person_rounded,
                    label: 'Profile',
                    index: 3,
                    isSmallScreen: isSmallScreen,
                    isTablet: isTablet,
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildNavItem({
    required BuildContext context,
    required IconData icon,
    required String label,
    required int index,
    required bool isSmallScreen,
    required bool isTablet,
  }) {
    final bool isSelected = currentIndex == index;
    final iconSize = isTablet ? 28.0 : (isSmallScreen ? 20.0 : 24.0);
    final fontSize = isTablet ? 14.0 : (isSmallScreen ? 10.0 : 12.0);
    final horizontalPadding = isTablet ? 20.0 : (isSmallScreen ? 8.0 : 16.0);

    return GestureDetector(
      onTap: () => onTap(index),
      behavior: HitTestBehavior.opaque,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        padding: EdgeInsets.symmetric(
          horizontal: horizontalPadding,
          vertical: isTablet ? 10.0 : 8.0,
        ),
        decoration: BoxDecoration(
          color: isSelected
              ? AppColors.primary.withOpacity(0.1)
              : Colors.transparent,
          borderRadius: BorderRadius.circular(isTablet ? 24.0 : 20.0),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              color: isSelected ? AppColors.primary : AppColors.textSecondary,
              size: iconSize,
            ),
            SizedBox(height: isTablet ? 6.0 : 4.0),
            Text(
              label,
              style: TextStyle(
                color: isSelected ? AppColors.primary : AppColors.textSecondary,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
                fontSize: fontSize,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }
}
