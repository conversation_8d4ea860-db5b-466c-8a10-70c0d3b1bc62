import 'package:flutter/material.dart';

import '../../theme/colors.dart';
import 'ribbon_badge.dart';

class FeatureCard extends StatelessWidget {
  final String title;
  final IconData icon;
  final Color? iconColor;
  final Color? backgroundColor;
  final VoidCallback onTap;
  final Widget? badge;
  final String? subtitle;
  final bool isOutlined;
  final bool showRibbonBadge;
  final String ribbonText;

  const FeatureCard({
    super.key,
    required this.title,
    required this.icon,
    this.iconColor,
    this.backgroundColor,
    required this.onTap,
    this.badge,
    this.subtitle,
    this.isOutlined = false,
    this.showRibbonBadge = false,
    this.ribbonText = 'Due',
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Stack(
        clipBehavior: Clip.none,
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color:
                  isOutlined ? Colors.white : backgroundColor ?? Colors.white,
              borderRadius: BorderRadius.circular(16),
              border: isOutlined
                  ? Border.all(color: AppColors.coolGrey.withOpacity(0.3))
                  : null,
              boxShadow: isOutlined
                  ? null
                  : [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.08),
                        offset: const Offset(0, 4),
                        blurRadius: 12,
                        spreadRadius: 0,
                      ),
                      BoxShadow(
                        color: Colors.black.withOpacity(0.03),
                        offset: const Offset(0, 2),
                        blurRadius: 6,
                        spreadRadius: -2,
                      ),
                    ],
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisSize: MainAxisSize.min,
              children: [
                Flexible(
                  flex: 2,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Container(
                        padding: const EdgeInsets.all(6),
                        decoration: BoxDecoration(
                          color:
                              (iconColor ?? AppColors.primary).withOpacity(0.1),
                          border: Border.all(
                              color: AppColors.coolGrey.withOpacity(0.3)),
                          borderRadius: BorderRadius.circular(10),
                        ),
                        child: Icon(
                          icon,
                          color: iconColor ?? AppColors.primary,
                          size: 20,
                        ),
                      ),
                      if (badge != null && !showRibbonBadge)
                        Flexible(child: badge!),
                    ],
                  ),
                ),
                const SizedBox(height: 8),
                Flexible(
                  flex: 3,
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        title,
                        style: const TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                        textAlign: TextAlign.center,
                      ),
                      if (subtitle != null) ...[
                        const SizedBox(height: 2),
                        Text(
                          subtitle!,
                          style: TextStyle(
                            fontSize: 11,
                            color: AppColors.textSecondary,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ],
                  ),
                ),
              ],
            ),
          ),
          if (showRibbonBadge) RibbonBadge(text: ribbonText),
        ],
      ),
    );
  }
}
