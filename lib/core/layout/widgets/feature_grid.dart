import 'package:flutter/material.dart';
import '../../theme/colors.dart';
import 'feature_card.dart';

class FeatureGrid extends StatelessWidget {
  final List<FeatureModel> features;
  final String? title;
  final String? viewAllText;
  final VoidCallback? onViewAllTap;
  final int crossAxisCount;
  final double spacing;
  final bool isScrollable;
  final EdgeInsetsGeometry? padding;
  final bool showOutlinedCards;
  final double? childAspectRatio;

  const FeatureGrid({
    Key? key,
    required this.features,
    this.title,
    this.viewAllText,
    this.onViewAllTap,
    this.crossAxisCount = 2,
    this.spacing = 16,
    this.isScrollable = true,
    this.padding,
    this.showOutlinedCards = false,
    this.childAspectRatio,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (title != null) ...[
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  title!,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                if (onViewAllTap != null)
                  TextButton(
                    onPressed: onViewAllTap,
                    child: Row(
                      children: [
                        Text(
                          viewAllText ?? 'View All',
                          style: TextStyle(
                            color: AppColors.primary,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        const SizedBox(width: 4),
                        Icon(
                          Icons.arrow_forward_rounded,
                          size: 16,
                          color: AppColors.primary,
                        ),
                      ],
                    ),
                  ),
              ],
            ),
          ),
        ],
        if (isScrollable)
          _buildScrollableGrid()
        else
          Padding(
            padding: padding ?? const EdgeInsets.all(16),
            child: _buildFixedGrid(),
          ),
      ],
    );
  }

  Widget _buildScrollableGrid() {
    return SingleChildScrollView(
      padding: padding ?? const EdgeInsets.all(16),
      physics: const BouncingScrollPhysics(),
      child: _buildFixedGrid(),
    );
  }

  Widget _buildFixedGrid() {
    return LayoutBuilder(
      builder: (context, constraints) {
        // Calculate crossAxisCount based on screen width
        final screenWidth = constraints.maxWidth;
        int dynamicCrossAxisCount;
        double dynamicAspectRatio;

        if (screenWidth < 360) {
          dynamicCrossAxisCount = 1; // Extra small screens
          dynamicAspectRatio = 1.5;
        } else if (screenWidth < 600) {
          dynamicCrossAxisCount = 2; // Mobile phones
          dynamicAspectRatio = childAspectRatio ?? 1.25;
        } else if (screenWidth < 900) {
          dynamicCrossAxisCount = 3; // Tablets
          dynamicAspectRatio = childAspectRatio ?? 1.3;
        } else {
          dynamicCrossAxisCount = 4; // Large tablets/desktops
          dynamicAspectRatio = childAspectRatio ?? 1.4;
        }

        return GridView.builder(
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount:
                crossAxisCount == 2 ? dynamicCrossAxisCount : crossAxisCount,
            childAspectRatio: 0.9, // Reduced from 1.1 to give more vertical space
            crossAxisSpacing: spacing,
            mainAxisSpacing: spacing,
          ),
          itemCount: features.length,
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemBuilder: (context, index) {
            final feature = features[index];
            return FeatureCard(
              title: feature.title,
              icon: feature.icon,
              iconColor: feature.iconColor,
              backgroundColor: feature.backgroundColor,
              onTap: feature.onTap,
              badge: feature.badge,
              subtitle: feature.subtitle,
              isOutlined: showOutlinedCards,
              showRibbonBadge: feature.showRibbonBadge,
              ribbonText: feature.ribbonText,
            );
          },
        );
      },
    );
  }
}

class FeatureModel {
  final String title;
  final IconData icon;
  final Color? iconColor;
  final Color? backgroundColor;
  final VoidCallback onTap;
  final Widget? badge;
  final String? subtitle;
  final bool showRibbonBadge;
  final String ribbonText;

  const FeatureModel({
    required this.title,
    required this.icon,
    this.iconColor,
    this.backgroundColor,
    required this.onTap,
    this.badge,
    this.subtitle,
    this.showRibbonBadge = false,
    this.ribbonText = 'Due',
  });
}
