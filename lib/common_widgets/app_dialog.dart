import 'package:flutter/material.dart';
import '../core/theme/colors.dart';
import 'app_button.dart';

class AppDialog extends StatelessWidget {
  final String title;
  final String message;
  final String? confirmButtonText;
  final String? cancelButtonText;
  final VoidCallback? onConfirm;
  final VoidCallback? onCancel;
  final Widget? icon;
  final Color iconColor;
  final bool barrierDismissible;
  final EdgeInsetsGeometry contentPadding;
  final double borderRadius;

  const AppDialog({
    Key? key,
    required this.title,
    required this.message,
    this.confirmButtonText,
    this.cancelButtonText,
    this.onConfirm,
    this.onCancel,
    this.icon,
    this.iconColor = AppColors.primary,
    this.barrierDismissible = true,
    this.contentPadding = const EdgeInsets.all(24),
    this.borderRadius = 16,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(borderRadius),
      ),
      child: Padding(
        padding: contentPadding,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (icon != null) ...[icon!, const SizedBox(height: 16)],
            Text(
              title,
              style: const TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            Text(
              message,
              style: const TextStyle(fontSize: 14, color: AppColors.textLight),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            _buildButtons(context),
          ],
        ),
      ),
    );
  }

  Widget _buildButtons(BuildContext context) {
    // If both buttons are provided, show them in a row
    if (confirmButtonText != null && cancelButtonText != null) {
      return Row(
        children: [
          Expanded(
            child: AppButton(
              label: cancelButtonText!,
              onPressed: () {
                if (onCancel != null) {
                  onCancel!();
                } else {
                  Navigator.of(context).pop(false);
                }
              },
              isOutlined: true,
              backgroundColor: AppColors.darkGrey,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: AppButton(
              label: confirmButtonText!,
              onPressed: () {
                if (onConfirm != null) {
                  onConfirm!();
                } else {
                  Navigator.of(context).pop(true);
                }
              },
              backgroundColor: AppColors.darkGrey,
            ),
          ),
        ],
      );
    }

    // If only confirm button is provided
    if (confirmButtonText != null) {
      return AppButton(
        label: confirmButtonText!,
        onPressed: () {
          if (onConfirm != null) {
            onConfirm!();
          } else {
            Navigator.of(context).pop(true);
          }
        },
        backgroundColor: AppColors.darkGrey,
      );
    }

    // If only cancel button is provided
    if (cancelButtonText != null) {
      return AppButton(
        label: cancelButtonText!,
        onPressed: () {
          if (onCancel != null) {
            onCancel!();
          } else {
            Navigator.of(context).pop(false);
          }
        },
        isOutlined: true,
        backgroundColor: AppColors.darkGrey,
      );
    }

    // Default case - just a confirm button
    return AppButton(
      label: 'OK',
      onPressed: () => Navigator.of(context).pop(),
      backgroundColor: AppColors.darkGrey,
    );
  }

  // Static methods to show different types of dialogs
  static Future<bool?> showConfirmDialog(
    BuildContext context, {
    required String title,
    required String message,
    String confirmText = 'Confirm',
    String cancelText = 'Cancel',
    VoidCallback? onConfirm,
    VoidCallback? onCancel,
    bool barrierDismissible = true,
  }) {
    return showDialog<bool>(
      context: context,
      barrierDismissible: barrierDismissible,
      builder: (context) => AppDialog(
        title: title,
        message: message,
        confirmButtonText: confirmText,
        cancelButtonText: cancelText,
        onConfirm: onConfirm,
        onCancel: onCancel,
        barrierDismissible: barrierDismissible,
      ),
    );
  }

  static Future<void> showSuccessDialog(
    BuildContext context, {
    required String title,
    required String message,
    String buttonText = 'OK',
    VoidCallback? onConfirm,
    bool barrierDismissible = true,
  }) {
    return showDialog<void>(
      context: context,
      barrierDismissible: barrierDismissible,
      builder: (context) => AppDialog(
        title: title,
        message: message,
        confirmButtonText: buttonText,
        onConfirm: onConfirm,
        icon: const Icon(
          Icons.check_circle,
          color: AppColors.success,
          size: 64,
        ),
        iconColor: AppColors.success,
        barrierDismissible: barrierDismissible,
      ),
    );
  }

  static Future<void> showErrorDialog(
    BuildContext context, {
    required String title,
    required String message,
    String buttonText = 'OK',
    VoidCallback? onConfirm,
    bool barrierDismissible = true,
  }) {
    return showDialog<void>(
      context: context,
      barrierDismissible: barrierDismissible,
      builder: (context) => AppDialog(
        title: title,
        message: message,
        confirmButtonText: buttonText,
        onConfirm: onConfirm,
        icon: const Icon(
          Icons.error_outline,
          color: AppColors.error,
          size: 64,
        ),
        iconColor: AppColors.error,
        barrierDismissible: barrierDismissible,
      ),
    );
  }
}
