import 'package:flutter/material.dart';
import '../core/theme/colors.dart';

class AppButton extends StatelessWidget {
  final String label;
  final VoidCallback? onPressed;
  final bool isLoading;
  final bool isGradient;
  final bool isOutlined;
  final double? width;
  final double height;
  final double borderRadius;
  final Color backgroundColor;
  final Color textColor;
  final IconData? prefixIcon;
  final IconData? suffixIcon;
  final double fontSize;
  final FontWeight fontWeight;
  final EdgeInsetsGeometry padding;

  const AppButton({
    Key? key,
    required this.label,
    required this.onPressed,
    this.isLoading = false,
    this.isGradient = true,
    this.isOutlined = false,
    this.width,
    this.height = 58,
    this.borderRadius = 12,
    this.backgroundColor = AppColors.darkGrey,
    this.textColor = Colors.white,
    this.prefixIcon,
    this.suffixIcon,
    this.fontSize = 16,
    this.fontWeight = FontWeight.bold,
    this.padding = const EdgeInsets.symmetric(horizontal: 16),
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final buttonStyle = ElevatedButton.styleFrom(
      backgroundColor: isOutlined ? Colors.transparent : backgroundColor,
      foregroundColor: isOutlined ? backgroundColor : textColor,
      disabledBackgroundColor: AppColors.lightGrey,
      disabledForegroundColor: AppColors.coolGrey,
      padding: padding,
      minimumSize: Size(width ?? double.infinity, height),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(borderRadius),
        side: isOutlined
            ? BorderSide(color: backgroundColor, width: 1.5)
            : BorderSide.none,
      ),
      elevation: isOutlined ? 0 : 0,
    );

    Widget buttonContent = Row(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        if (prefixIcon != null && !isLoading) ...[
          Icon(
            prefixIcon,
            size: fontSize + 4,
            color: Colors.white,
          ),
          //const SizedBox(width: 8),
        ],
        if (isLoading)
          SizedBox(
            height: fontSize + 4,
            width: fontSize + 4,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              color: isOutlined ? backgroundColor : textColor,
            ),
          )
        else
          Text(
            label,
            style: TextStyle(fontSize: fontSize, fontWeight: fontWeight),
          ),
        if (suffixIcon != null && !isLoading) ...[
          const SizedBox(width: 8),
          Icon(suffixIcon, size: fontSize + 4, color: textColor),
        ],
      ],
    );

    if (isGradient && !isOutlined) {
      return Container(
        width: width ?? double.infinity,
        height: height,
        decoration: BoxDecoration(
          gradient: AppColors.blackToGreyGradient,
          borderRadius: BorderRadius.circular(borderRadius),
        ),
        child: ElevatedButton(
          onPressed: isLoading ? null : onPressed,
          style: buttonStyle.copyWith(
            backgroundColor: MaterialStateProperty.all(Colors.transparent),
            shadowColor: MaterialStateProperty.all(Colors.transparent),
          ),
          child: buttonContent,
        ),
      );
    }

    return ElevatedButton(
      onPressed: isLoading ? null : onPressed,
      style: buttonStyle,
      child: buttonContent,
    );
  }
}
