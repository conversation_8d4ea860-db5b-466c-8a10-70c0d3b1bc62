import 'package:flutter/material.dart';

class HouseholdStyleAppBar extends StatelessWidget
    implements PreferredSizeWidget {
  final String title;
  final bool showBackButton;
  final bool showNotificationButton;
  final bool showGridButton;
  final bool showProfileButton;
  final VoidCallback? onNotificationPressed;
  final VoidCallback? onGridPressed;
  final VoidCallback? onProfilePressed;
  final VoidCallback? onBackPressed;

  const HouseholdStyleAppBar({
    Key? key,
    required this.title,
    this.showBackButton = true,
    this.showNotificationButton = true,
    this.showGridButton = true,
    this.showProfileButton = true,
    this.onNotificationPressed,
    this.onGridPressed,
    this.onProfilePressed,
    this.onBackPressed,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return AppBar(
      elevation: 0,
      flexibleSpace: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            colors: [Color(0xFFC02425), Color(0xFF931313)],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
      ),
      title: Text(
        title,
        style: const TextStyle(
          color: Colors.white,
          fontWeight: FontWeight.bold,
          fontSize: 20,
        ),
      ),
      centerTitle: true,
      leading: showBackButton
          ? IconButton(
              icon: const Icon(Icons.arrow_back, color: Colors.white),
              onPressed: onBackPressed ?? () => Navigator.of(context).pop(),
            )
          : null,
      automaticallyImplyLeading: showBackButton,
      actions: [
        if (showNotificationButton)
          IconButton(
            icon: const Icon(Icons.notifications_outlined, color: Colors.white),
            onPressed: onNotificationPressed ?? () {},
          ),
        if (showGridButton)
          IconButton(
            icon: const Icon(Icons.grid_view_rounded, color: Colors.white),
            onPressed: onGridPressed ?? () {},
          ),
        if (showProfileButton)
          IconButton(
            icon: const Icon(Icons.person_outline, color: Colors.white),
            onPressed: onProfilePressed ?? () {},
          ),
      ],
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}
