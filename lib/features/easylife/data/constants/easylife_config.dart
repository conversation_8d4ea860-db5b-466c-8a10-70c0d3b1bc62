import 'package:flutter/material.dart';

/// EasyLife Configuration
///
/// This class provides configuration options for the EasyLife feature module.
/// It replaces the EasyLifeConfig and integrates seamlessly with the main app.
class EasyLifeConfig {
  /// API base URL for EasyLife services
  final String apiBaseUrl;

  /// API key for authentication
  final String apiKey;

  /// Whether to enable analytics
  final bool enableAnalytics;

  /// Whether to enable crash reporting
  final bool enableCrashReporting;

  /// The theme mode to use
  final ThemeMode themeMode;

  /// The locale to use
  final Locale? locale;

  /// Whether to enable debug mode
  final bool debugMode;

  /// Razorpay key for payments
  final String? razorpayKey;

  /// Firebase configuration
  final FirebaseConfig? firebaseConfig;

  /// Google Maps API key
  final String? googleMapsApiKey;

  /// Create a new EasyLifeConfig instance
  const EasyLifeConfig({
    this.apiBaseUrl = 'https://api.eassylife.com',
    this.apiKey = '',
    this.enableAnalytics = true,
    this.enableCrashReporting = true,
    this.themeMode = ThemeMode.system,
    this.locale,
    this.debugMode = false,
    this.razorpayKey,
    this.firebaseConfig,
    this.googleMapsApiKey,
  });

  /// Create a copy of this configuration with some values replaced
  EasyLifeConfig copyWith({
    String? apiBaseUrl,
    String? apiKey,
    bool? enableAnalytics,
    bool? enableCrashReporting,
    ThemeMode? themeMode,
    Locale? locale,
    bool? debugMode,
    String? razorpayKey,
    FirebaseConfig? firebaseConfig,
    String? googleMapsApiKey,
  }) {
    return EasyLifeConfig(
      apiBaseUrl: apiBaseUrl ?? this.apiBaseUrl,
      apiKey: apiKey ?? this.apiKey,
      enableAnalytics: enableAnalytics ?? this.enableAnalytics,
      enableCrashReporting: enableCrashReporting ?? this.enableCrashReporting,
      themeMode: themeMode ?? this.themeMode,
      locale: locale ?? this.locale,
      debugMode: debugMode ?? this.debugMode,
      razorpayKey: razorpayKey ?? this.razorpayKey,
      firebaseConfig: firebaseConfig ?? this.firebaseConfig,
      googleMapsApiKey: googleMapsApiKey ?? this.googleMapsApiKey,
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'apiBaseUrl': apiBaseUrl,
      'apiKey': apiKey,
      'enableAnalytics': enableAnalytics,
      'enableCrashReporting': enableCrashReporting,
      'themeMode': themeMode.name,
      'locale': locale?.toString(),
      'debugMode': debugMode,
      'razorpayKey': razorpayKey,
      'firebaseConfig': firebaseConfig?.toJson(),
      'googleMapsApiKey': googleMapsApiKey,
    };
  }

  /// Create from JSON
  factory EasyLifeConfig.fromJson(Map<String, dynamic> json) {
    return EasyLifeConfig(
      apiBaseUrl: json['apiBaseUrl'] ?? 'https://api.eassylife.com',
      apiKey: json['apiKey'] ?? '',
      enableAnalytics: json['enableAnalytics'] ?? true,
      enableCrashReporting: json['enableCrashReporting'] ?? true,
      themeMode: ThemeMode.values.firstWhere(
        (mode) => mode.name == json['themeMode'],
        orElse: () => ThemeMode.system,
      ),
      locale: json['locale'] != null ? Locale(json['locale']) : null,
      debugMode: json['debugMode'] ?? false,
      razorpayKey: json['razorpayKey'],
      firebaseConfig: json['firebaseConfig'] != null
          ? FirebaseConfig.fromJson(json['firebaseConfig'])
          : null,
      googleMapsApiKey: json['googleMapsApiKey'],
    );
  }

  @override
  String toString() {
    return 'EasyLifeConfig(apiBaseUrl: $apiBaseUrl, apiKey: ${apiKey.isNotEmpty ? '***' : 'empty'}, enableAnalytics: $enableAnalytics, enableCrashReporting: $enableCrashReporting, themeMode: $themeMode, locale: $locale, debugMode: $debugMode)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is EasyLifeConfig &&
        other.apiBaseUrl == apiBaseUrl &&
        other.apiKey == apiKey &&
        other.enableAnalytics == enableAnalytics &&
        other.enableCrashReporting == enableCrashReporting &&
        other.themeMode == themeMode &&
        other.locale == locale &&
        other.debugMode == debugMode &&
        other.razorpayKey == razorpayKey &&
        other.firebaseConfig == firebaseConfig &&
        other.googleMapsApiKey == googleMapsApiKey;
  }

  @override
  int get hashCode {
    return Object.hash(
      apiBaseUrl,
      apiKey,
      enableAnalytics,
      enableCrashReporting,
      themeMode,
      locale,
      debugMode,
      razorpayKey,
      firebaseConfig,
      googleMapsApiKey,
    );
  }
}

/// Firebase Configuration
class FirebaseConfig {
  final String? projectId;
  final String? appId;
  final String? apiKey;
  final String? messagingSenderId;

  const FirebaseConfig({
    this.projectId,
    this.appId,
    this.apiKey,
    this.messagingSenderId,
  });

  Map<String, dynamic> toJson() {
    return {
      'projectId': projectId,
      'appId': appId,
      'apiKey': apiKey,
      'messagingSenderId': messagingSenderId,
    };
  }

  factory FirebaseConfig.fromJson(Map<String, dynamic> json) {
    return FirebaseConfig(
      projectId: json['projectId'],
      appId: json['appId'],
      apiKey: json['apiKey'],
      messagingSenderId: json['messagingSenderId'],
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is FirebaseConfig &&
        other.projectId == projectId &&
        other.appId == appId &&
        other.apiKey == apiKey &&
        other.messagingSenderId == messagingSenderId;
  }

  @override
  int get hashCode {
    return Object.hash(projectId, appId, apiKey, messagingSenderId);
  }
}

/// Default EasyLife configuration
const kDefaultEasyLifeConfig = EasyLifeConfig(
  apiBaseUrl: 'https://api.eassylife.com',
  apiKey: 'eassylife_oneapp_api_key_2023',
  enableAnalytics: true,
  enableCrashReporting: true,
  themeMode: ThemeMode.system,
  debugMode: false,
);
