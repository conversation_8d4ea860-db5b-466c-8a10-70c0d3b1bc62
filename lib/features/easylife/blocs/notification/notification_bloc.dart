import 'package:hydrated_bloc/hydrated_bloc.dart';
import 'package:easylife/blocs/notification/notification_event.dart';
import 'package:easylife/blocs/notification/notification_state.dart';

class NotificationBloc extends Bloc<NotificationEvent, NotificationState> {
  NotificationBloc() : super(NotificationState()) {
    on<FetchNotificationDataEvent>(_onFetchNotificationData);
    on<RefreshNotificationDataEvent>(_onRefreshNotificationData);
  }

  Future<void> _onFetchNotificationData(
      FetchNotificationDataEvent event, Emitter<NotificationState> emit) async {
    try {
      emit(state.copyWith(status: NotificationStatus.notificationLoading));
      // final notificationResponse = await apiValue.getNotificationList(event.context);
      final notificationResponse = await {"data": []};

      List favList = [];

      // if (notificationResponse != null) {
      favList = notificationResponse['data'] as List;

      if (favList.isNotEmpty) {
        emit(state.copyWith(
            status: NotificationStatus.success, notificationList: favList));
      } else {
        emit(state.copyWith(
            status: NotificationStatus.noDataFound, notificationList: favList));
      }
      // } else {
      //   emit(state.copyWith(status: NotificationStatus.noDataFound));
      // }
    } catch (e) {
      emit(state.copyWith(
          status: NotificationStatus.failure, errorMessage: e.toString()));
    }
  }

  Future<void> _onRefreshNotificationData(
    RefreshNotificationDataEvent event,
    Emitter<NotificationState> emit,
  ) async {
    await _onFetchNotificationData(
        FetchNotificationDataEvent(context: event.context), emit);
  }
}
