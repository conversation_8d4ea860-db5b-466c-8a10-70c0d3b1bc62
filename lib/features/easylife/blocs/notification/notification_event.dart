import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';

abstract class NotificationEvent extends Equatable {
  const NotificationEvent();

  @override
  List<Object?> get props => [];
}

class FetchNotificationDataEvent extends NotificationEvent {
  final BuildContext context;
  const FetchNotificationDataEvent({required this.context});

  @override
  List<Object?> get props => [context];
}

class RefreshNotificationDataEvent extends NotificationEvent {
  final BuildContext context;
  const RefreshNotificationDataEvent({required this.context});

  @override
  List<Object?> get props => [context];
}
