import 'package:equatable/equatable.dart';

class NotificationState extends Equatable {
  final List notificationList;
  final NotificationStatus status;
  final String? errorMessage;

  const NotificationState({
    this.notificationList = const [],
    this.status = NotificationStatus.initial,
    this.errorMessage,
  });

  NotificationState copyWith({
    List? notificationList,
    NotificationStatus? status,
    String? errorMessage,
  }) {
    return NotificationState(
      notificationList: notificationList ?? this.notificationList,
      status: status ?? this.status,
      errorMessage: errorMessage,
    );
  }

  @override
  List<Object?> get props => [
        notificationList,
        status,
        errorMessage,
      ];
}

enum NotificationStatus {
  initial,
  notificationLoading,
  success,
  noDataFound,
  failure,
}
