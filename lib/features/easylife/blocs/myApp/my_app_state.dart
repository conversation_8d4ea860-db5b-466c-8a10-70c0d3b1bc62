import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';

class MyAppState extends Equatable {
  final Locale? locale;
  final String? errorMessage;

  const MyAppState({
    this.locale,
    this.errorMessage,
  });

  MyAppState copyWith({
    Locale? locale,
    String? errorMessage,
  }) {
    return MyAppState(
      locale: locale ?? this.locale,
      errorMessage: errorMessage,
    );
  }

  @override
  List<Object?> get props => [
        locale,
        errorMessage,
      ];
}

// enum MyAppStatus { initial }
