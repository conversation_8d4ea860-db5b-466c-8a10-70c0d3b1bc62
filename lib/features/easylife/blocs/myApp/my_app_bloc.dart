import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:hydrated_bloc/hydrated_bloc.dart';
import 'package:easylife/blocs/myApp/my_app_event.dart';
import 'package:easylife/blocs/myApp/my_app_state.dart';
import '../data/datasources/local/shared_pref_helper.dart';
import '../utils/appData/app_strings.dart';

class MyAppBloc extends Bloc<MyAppEvent, MyAppState> {
  MyAppBloc() : super(MyAppState()) {
    on<InitialEvent>(_onInitial);
    on<SwitchLanguageEvent>(_onLangSwtchToggle);
  }

  _onInitial(InitialEvent event, Emitter<MyAppState> emit) async {
    String language = await SharedPreferencesHelper.getPreferredLanguage();
    _updateLanguageState(language, emit);
  }

  _onLangSwtchToggle(
      SwitchLanguageEvent event, Emitter<MyAppState> emit) async {
    SharedPreferencesHelper.setPreferredLanguage(lang: event.lang);
    _updateLanguageState(event.lang, emit);
  }

  void _updateLanguageState(String language, Emitter<MyAppState> emit) {
    debugPrint('-----------------------------------bloc');
    debugPrint('language = $language');

    if (language.isEmpty) {
      emit(state.copyWith(locale: const Locale("en")));
      return;
    }

    debugPrint('================================');

    switch (language) {
      case AppStrings.englishProfile:
        debugPrint('english');
        emit(state.copyWith(locale: const Locale("en")));
        break;
      case AppStrings.hindiProfile:
        debugPrint('hindi');
        emit(state.copyWith(locale: const Locale("hi")));
        break;
      case AppStrings.marathiProfile:
        debugPrint('marathi');
        emit(state.copyWith(locale: const Locale("mr")));
        break;
      case AppStrings.hinglishProfile:
        debugPrint('hinglish');
        emit(state.copyWith(locale: const Locale("or")));
        break;
      default:
        debugPrint('english');
        emit(state.copyWith(locale: const Locale("en")));
    }
  }
}
