import 'package:equatable/equatable.dart';
import 'package:video_player/video_player.dart';

class VideoState extends Equatable {
  final VideoPlayerController? controller;
  final bool isInitialized;
  final bool isMuted;
  final bool isBuffering;
  final bool hasError;

  const VideoState({
    this.controller,
    this.isInitialized = false,
    this.isMuted = true,
    this.isBuffering = false,
    this.hasError = false,
  });

  VideoState copyWith({
    VideoPlayerController? controller,
    bool? isInitialized,
    bool? isMuted,
    bool? isBuffering,
    bool? hasError,
  }) {
    return VideoState(
      controller: controller ?? this.controller,
      isInitialized: isInitialized ?? this.isInitialized,
      isMuted: isMuted ?? this.isMuted,
      isBuffering: isBuffering ?? this.isBuffering,
      hasError: hasError ?? this.hasError,
    );
  }

  @override
  List<Object?> get props => [
        controller,
        isInitialized,
        isMuted,
        isBuffering,
        hasError,
      ];
}
