import 'package:equatable/equatable.dart';

abstract class VideoEvent extends Equatable {
  @override
  List<Object?> get props => [];
}

class VideoInitializeEvent extends VideoEvent {
  final String videoUrl;
  VideoInitializeEvent({required this.videoUrl});

  @override
  List<Object?> get props => [videoUrl];
}

class VideoToggleMuteEvent extends VideoEvent {}

class VideoBufferingChangedEvent extends VideoEvent {
  final bool isBuffering;
  VideoBufferingChangedEvent(this.isBuffering);

  @override
  List<Object?> get props => [isBuffering];
}

class VideoPauseEvent extends VideoEvent {}

class VideoResumeEvent extends VideoEvent {}
