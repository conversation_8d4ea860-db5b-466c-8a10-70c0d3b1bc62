import 'package:hydrated_bloc/hydrated_bloc.dart';
import 'package:easylife/blocs/videoPlayer/video_player_event.dart';
import 'package:easylife/blocs/videoPlayer/video_player_state.dart';
import 'package:video_player/video_player.dart';

class VideoBloc extends Bloc<VideoEvent, VideoState> {
  VideoBloc() : super(VideoState()) {
    on<VideoInitializeEvent>(_onInitialize);
    on<VideoToggleMuteEvent>(_onToggleMute);
    on<VideoBufferingChangedEvent>(_onBufferingChanged);
    on<VideoPauseEvent>(_onPause);
    on<VideoResumeEvent>(_onResume);
  }

  Future<void> _onInitialize(
      VideoInitializeEvent event, Emitter<VideoState> emit) async {
    try {
      final controller = VideoPlayerController.networkUrl(
        Uri.parse(event.videoUrl),
        videoPlayerOptions: VideoPlayerOptions(allowBackgroundPlayback: true),
      );

      await controller.initialize();
      controller.setLooping(true);
      controller.play();
      controller.setVolume(0.0);

      controller.addListener(() {
        add(VideoBufferingChangedEvent(controller.value.isBuffering));
      });

      emit(state.copyWith(controller: controller, isInitialized: true));
    } catch (error) {
      emit(state.copyWith(hasError: true));
    }
  }

  void _onToggleMute(VideoToggleMuteEvent event, Emitter<VideoState> emit) {
    final newMuteState = !state.isMuted;
    state.controller?.setVolume(newMuteState ? 0.0 : 1.0);
    emit(state.copyWith(isMuted: newMuteState));
  }

  void _onBufferingChanged(
      VideoBufferingChangedEvent event, Emitter<VideoState> emit) {
    emit(state.copyWith(isBuffering: event.isBuffering));
  }

  void _onPause(VideoPauseEvent event, Emitter<VideoState> emit) {
    state.controller?.pause();
  }

  void _onResume(VideoResumeEvent event, Emitter<VideoState> emit) {
    state.controller?.play();
  }

  @override
  Future<void> close() {
    state.controller?.dispose();
    return super.close();
  }
}
