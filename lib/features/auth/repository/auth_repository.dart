import 'package:dio/dio.dart';
import '../models/login_model.dart';
import '../models/login_request_model.dart';
import '../../../core/mcp/base_repository.dart';

class AuthRepository extends BaseRepository {
  AuthRepository(Dio dio) : super(dio);

  Future<LoginModel> login(LoginRequestModel request) async {
    final response = await post<Map<String, dynamic>>(
      '/auth/login',
      data: request.toJson(),
    );
    return LoginModel.fromJson(response.data!);
  }

  Future<void> logout() async {
    await post('/auth/logout');
  }

  Future<LoginModel> refreshToken(String refreshToken) async {
    final response = await post<Map<String, dynamic>>(
      '/auth/refresh-token',
      data: {'refreshToken': refreshToken},
    );
    return LoginModel.fromJson(response.data!);
  }
}
