import 'package:flutter/material.dart';
import '../models/login_request_model.dart';
import '../repository/auth_repository.dart';
import '../../../core/mvp/base_presenter.dart';
import '../../../core/mvp/base_view.dart';

abstract class LoginView extends BaseView {
  void onLoginSuccess();
  void onLoginError(String message);
}

class LoginPresenter extends BasePresenter<LoginView> {
  final AuthRepository _authRepository;
  
  LoginPresenter(this._authRepository);
  
  Future<void> login(String email, String password) async {
    if (!isViewAttached) return;
    
    view?.showLoading();
    
    try {
      final request = LoginRequestModel(email: email, password: password);
      final response = await _authRepository.login(request);
      
      // Save tokens and user data to secure storage/local storage
      // For example:
      // await _secureStorage.write(key: 'token', value: response.token);
      // await _secureStorage.write(key: 'refresh_token', value: response.refreshToken);
      
      if (isViewAttached) {
        view?.hideLoading();
        view?.onLoginSuccess();
      }
    } catch (e) {
      if (isViewAttached) {
        view?.hideLoading();
        view?.onLoginError(e.toString());
      }
    }
  }
  
  @override
  void onViewInitialized(BuildContext context) {
    // Initialize any required data when view is initialized
  }
  
  @override
  void dispose() {
    // Clean up any resources
  }
}
