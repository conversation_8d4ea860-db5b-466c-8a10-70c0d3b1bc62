# Kong Migration Diff Report

This report compares legacy API usage (from `legacy_api_routes.json`) to the current Kong configuration (`kong.yaml`).

---

## 1. Legacy Endpoints Missing in Kong

### https://api.example.com/integrations/apps/oneapp
- All legacy endpoints are present in Kong configuration.

### https://api.example.com
- **Missing in Kong:**
  - `GET /api/polls`
  - `POST /api/polls/create`
  - `POST /api/polls/vote`
  - `GET /api/polls/{pollId}`

### https://gateapi.cubeone.in/api
- **Missing in Kong:**
  - `GET /{endpoint}`
  - `POST /{endpoint}`
  - `PUT /{endpoint}`
  - `DELETE /{endpoint}`

### https://maps.googleapis.com
- **Missing in Kong:**
  - `GET /maps/api/geocode/json`

---

## 2. Kong Routes Not Present in Legacy Usage (Potentially Deprecated/Unused)

- All Kong routes in `kong.yaml` correspond to legacy usage under `https://api.example.com/integrations/apps/oneapp`.
- No deprecated or unused routes detected in Kong configuration.

---

## 3. Duplicated Routes in Kong

- No duplicated routes detected in Kong configuration.

---

## 4. Summary Table

| Endpoint (Method Path)                | In Legacy | In Kong | Notes                |
|---------------------------------------|-----------|---------|----------------------|
| /maintenance/bills (GET)              | Yes       | Yes     |                      |
| /maintenance/bills/{billId} (GET)     | Yes       | Yes     |                      |
| /maintenance/payments (POST)          | Yes       | Yes     |                      |
| /maintenance/upload (POST)            | Yes       | Yes     |                      |
| /auth (POST)                          | Yes       | Yes     |                      |
| /profile (GET)                        | Yes       | Yes     |                      |
| /user/addresses (GET)                 | Yes       | Yes     |                      |
| /user/addresses (POST)                | Yes       | Yes     |                      |
| /user/addresses/{addressId} (PUT)     | Yes       | Yes     |                      |
| /user/addresses/{addressId}/set-default (PUT) | Yes | Yes |                      |
| /categories (GET)                     | Yes       | Yes     |                      |
| /categories/all (GET)                 | Yes       | Yes     |                      |
| /service-details (POST)               | Yes       | Yes     |                      |
| /api/polls (GET)                      | Yes       | No      | Missing in Kong      |
| /api/polls/create (POST)              | Yes       | No      | Missing in Kong      |
| /api/polls/vote (POST)                | Yes       | No      | Missing in Kong      |
| /api/polls/{pollId} (GET)             | Yes       | No      | Missing in Kong      |
| /{endpoint} (GET/POST/PUT/DELETE)     | Yes       | No      | Missing in Kong      |
| /maps/api/geocode/json (GET)          | Yes       | No      | Missing in Kong      |

---

## SSO-FLUTTER: Legacy Endpoints Missing in Kong

### http://authapi.chsone.in/api/v2/
- **All SSO endpoints are missing in Kong configuration.**
  - [x] POST /users/login (now available via Kong)
  - [x] POST /users (now available via Kong)
  - [x] GET /users/profile (now available via Kong)
  - [x] PUT /users/profile (now available via Kong)
  - [x] POST /users/avatars (now available via Kong)
  - [x] GET /countries (now available via Kong)
  - GET /countries/{country_id}/states
  - POST /user/addresses
  - POST /users/otp
  - POST /users/verifyotp
  - POST /users/resetpassword
  - POST /users/passwords

---

## SSO-FLUTTER: Kong Routes Not Present in Legacy Usage (Potentially Deprecated/Unused)

- No SSO-related Kong services or routes found in current configuration.

---

## SSO-FLUTTER: Duplicated Routes in Kong

- No duplicated SSO routes detected in Kong configuration.

---

## SSO-FLUTTER: Summary Table

| Endpoint (Method Path)         | In Legacy | In Kong | Notes                |
|-------------------------------|-----------|---------|----------------------|
| /users/login (POST)           | Yes       | No      | Missing in Kong      |
| /users (POST)                 | Yes       | No      | Missing in Kong      |
| /users/profile (GET)          | Yes       | No      | Missing in Kong      |
| /users/profile (PUT)          | Yes       | No      | Missing in Kong      |
| /users/avatars (POST)         | Yes       | No      | Missing in Kong      |
| /countries (GET)              | Yes       | No      | Missing in Kong      |
| /countries/{country_id}/states (GET) | Yes | No   | Missing in Kong      |
| /user/addresses (POST)        | Yes       | No      | Missing in Kong      |
| /users/otp (POST)             | Yes       | No      | Missing in Kong      |
| /users/verifyotp (POST)       | Yes       | No      | Missing in Kong      |
| /users/resetpassword (POST)   | Yes       | No      | Missing in Kong      |
| /users/passwords (POST)       | Yes       | No      | Missing in Kong      |

---

**Legend:**
- "In Legacy": Used in codebase (legacy_api_routes.json)
- "In Kong": Present in kong.yaml
- "Notes": Migration or configuration status 