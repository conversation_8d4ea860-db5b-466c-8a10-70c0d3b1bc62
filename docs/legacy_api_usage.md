# Legacy API Usage

This document lists all instances of HTTP calls found in the project, with line references and source files, grouped by HTTP client type and method.

---

## http.get
- `lib/services/maintenance_service.dart:11`  
- `lib/services/maintenance_service.dart:33`  
- `lib/modules/community/services/poll_service.dart:11`  
- `lib/modules/community/services/poll_service.dart:87`  
- `lib/core/services/api_service.dart:50`  
- `lib/core/utils/api_client.dart:27`  

## http.post
- `lib/services/maintenance_service.dart:84`  
- `lib/services/maintenance_service.dart:267`  
- `lib/services/maintenance_service.dart:290`  
- `lib/modules/community/services/poll_service.dart:44`  
- `lib/modules/community/services/poll_service.dart:68`  
- `lib/core/services/api_service.dart:80`  
- `lib/core/utils/api_client.dart:35`  

## http.put
- `lib/core/services/api_service.dart:114`  
- `lib/core/utils/api_client.dart:47`  

## http.delete
- `lib/core/utils/api_client.dart:56`  
- `lib/core/services/api_service.dart:142`  

## Dio().get
- `oneapp_flutter/lib/utils/common_functions.dart:477`  

## dio.get
- `oneapp_flutter/lib/services/oneapp_api_client.dart:24`  
- `oneapp_flutter/lib/data/remote/apiMethods.dart:121`  
- `oneapp_flutter/lib/services/oneapp_auth_service.dart:79`  
- `oneapp_flutter/lib/services/oneapp_address_service.dart:20`  
- `oneapp_flutter/lib/services/oneapp_category_service.dart:20`  
- `oneapp_flutter/lib/services/oneapp_category_service.dart:49`  

## dio.post
- `oneapp_flutter/lib/services/oneapp_api_client.dart:46`  
- `oneapp_flutter/lib/services/oneapp_address_service.dart:69`  
- `oneapp_flutter/lib/data/remote/apiMethods.dart:94`  
- `oneapp_flutter/lib/services/oneapp_auth_service.dart:41`  
- `oneapp_flutter/lib/services/oneapp_category_service.dart:90`  

## dio.put
- `oneapp_flutter/lib/services/oneapp_api_client.dart:69`  
- `oneapp_flutter/lib/services/oneapp_address_service.dart:119`  
- `oneapp_flutter/lib/services/oneapp_address_service.dart:150`  
- `oneapp_flutter/lib/data/remote/apiMethods.dart:174`  
- `oneapp_flutter/lib/data/remote/apiMethods.dart:202`  

## dio.delete
- `oneapp_flutter/lib/services/oneapp_api_client.dart:92`  
- `oneapp_flutter/lib/data/remote/apiMethods.dart:146`  

---

# SSO-FLUTTER LEGACY API USAGE

## http.get
- `sso-flutter/common_config/lib/utils/network/base_network.dart:113` (Dio().get)

## http.post
- `sso-flutter/common_config/lib/utils/network/base_network.dart:127` (Dio().post)
- `sso-flutter/common_config/lib/utils/network/base_network.dart:284` (Dio().post)
- `sso-flutter/common_config/lib/utils/network/base_network.dart:286` (http.post)
- `sso-flutter/lib/presentor/module/sso/profile/profile_model.dart:274` (dio.post)

## http.put
- `sso-flutter/common_config/lib/utils/network/base_network.dart:134` (Dio().put)

## http.delete
- `sso-flutter/common_config/lib/utils/network/base_network.dart:153` (http.delete)

## dio.get
- `sso-flutter/common_config/lib/utils/network/base_network.dart:113`

## dio.post
- `sso-flutter/common_config/lib/utils/network/base_network.dart:127`
- `sso-flutter/common_config/lib/utils/network/base_network.dart:284`
- `sso-flutter/lib/presentor/module/sso/profile/profile_model.dart:274`

## dio.put
- `sso-flutter/common_config/lib/utils/network/base_network.dart:134`

## dio.delete
(no direct matches found)

</rewritten_file> 