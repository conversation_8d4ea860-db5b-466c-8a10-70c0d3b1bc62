oneapp_route_migration_plan:
  - method: GET
    path: /maintenance/bills
    status: validated
    kong_service: oneapp-maintenance
    upstream_url: https://api.example.com/integrations/apps/oneapp

  - method: GET
    path: /maintenance/bills/{billId}
    status: validated
    kong_service: oneapp-maintenance
    upstream_url: https://api.example.com/integrations/apps/oneapp

  - method: POST
    path: /maintenance/payments
    status: validated
    kong_service: oneapp-maintenance
    upstream_url: https://api.example.com/integrations/apps/oneapp

  - method: POST
    path: /maintenance/upload
    status: validated
    kong_service: oneapp-maintenance
    upstream_url: https://api.example.com/integrations/apps/oneapp

  - method: POST
    path: /auth
    status: validated
    kong_service: oneapp-auth
    upstream_url: https://api.example.com/integrations/apps/oneapp

  - method: GET
    path: /profile
    status: validated
    kong_service: oneapp-auth
    upstream_url: https://api.example.com/integrations/apps/oneapp

  - method: GET
    path: /user/addresses
    status: validated
    kong_service: oneapp-address
    upstream_url: https://api.example.com/integrations/apps/oneapp

  - method: POST
    path: /user/addresses
    status: validated
    kong_service: oneapp-address
    upstream_url: https://api.example.com/integrations/apps/oneapp

  - method: PUT
    path: /user/addresses/{addressId}
    status: validated
    kong_service: oneapp-address
    upstream_url: https://api.example.com/integrations/apps/oneapp

  - method: PUT
    path: /user/addresses/{addressId}/set-default
    status: validated
    kong_service: oneapp-address
    upstream_url: https://api.example.com/integrations/apps/oneapp

  - method: GET
    path: /categories
    status: validated
    kong_service: oneapp-category
    upstream_url: https://api.example.com/integrations/apps/oneapp

  - method: GET
    path: /categories/all
    status: validated
    kong_service: oneapp-category
    upstream_url: https://api.example.com/integrations/apps/oneapp

  - method: POST
    path: /service-details
    status: validated
    kong_service: oneapp-category
    upstream_url: https://api.example.com/integrations/apps/oneapp

  - endpoint: /users/login
    method: POST
    service: sso-flutter-auth
    status: migrated

  - endpoint: /users
    method: POST
    service: sso-flutter-signup
    status: migrated

  - endpoint: /users/profile
    method: GET
    service: sso-flutter-profile
    status: migrated

  - endpoint: /users/avatars
    method: POST
    service: sso-flutter-avatar-upload
    status: migrated

  - endpoint: /countries
    method: GET
    service: sso-flutter-countries
    status: migrated 