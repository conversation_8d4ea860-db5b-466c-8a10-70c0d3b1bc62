# Legacy-to-Modern API Migration Reference Table

| Legacy Endpoint (Method Path)                | New Kong Route Name                   | Micro App                | Status    |
|----------------------------------------------|---------------------------------------|--------------------------|-----------|
| GET /maintenance/bills                       | oneapp-maintenance-bills-get          | OneApp > Maintenance     | migrated  |
| GET /maintenance/bills/{billId}              | oneapp-maintenance-bills-id-get       | OneApp > Maintenance     | migrated  |
| POST /maintenance/payments                   | oneapp-maintenance-payments-post      | OneApp > Maintenance     | migrated  |
| POST /maintenance/upload                     | oneapp-maintenance-upload-post        | OneApp > Maintenance     | migrated  |
| POST /auth                                   | oneapp-auth-post                     | OneApp > Auth            | migrated  |
| GET /profile                                 | oneapp-profile-get                    | OneApp > Auth/Profile    | migrated  |
| GET /user/addresses                          | oneapp-user-addresses-get             | OneApp > Address         | migrated  |
| POST /user/addresses                         | oneapp-user-addresses-post            | OneApp > Address         | migrated  |
| PUT /user/addresses/{addressId}              | oneapp-user-addresses-id-put          | OneApp > Address         | migrated  |
| PUT /user/addresses/{addressId}/set-default  | oneapp-user-addresses-id-set-default-put | OneApp > Address     | migrated  |
| GET /categories                              | oneapp-categories-get                 | OneApp > Category/Services | migrated  |
| GET /categories/all                          | oneapp-categories-all-get             | OneApp > Category/Services | migrated  |
| POST /service-details                        | oneapp-service-details-post            | OneApp > Category/Services | migrated  |
| POST /users/login                            | sso-flutter-auth-post                  | SSO > Auth > Login         | migrated  |
| POST /users                                  | sso-auth-users-post                   | SSO > Auth > Signup        | migrated  |
| GET /users/profile                           | sso-flutter-profile                    | SSO > Profile > View       | migrated  |
| PUT /users/profile                           | sso-auth-users-profile-put            | SSO > Profile > Update     | pending   |
| POST /users/avatars                          | sso-flutter-avatar-upload              | SSO > Profile > Avatar Upload | migrated  |
| GET /countries                               | sso-flutter-countries                  | SSO > Location > Country List | migrated  |
| GET /countries/{country_id}/states           | sso-auth-countries-states-get         | SSO > Location > State List   | pending   |
| POST /user/addresses                         | sso-auth-user-addresses-post          | SSO > Profile > Address      | pending   |
| POST /users/otp                              | sso-auth-users-otp-post               | SSO > Auth > OTP             | pending   |
| POST /users/verifyotp                        | sso-auth-users-verifyotp-post         | SSO > Auth > OTP Verify      | pending   |
| POST /users/resetpassword                    | sso-auth-users-resetpassword-post     | SSO > Auth > Reset Password  | pending   |
| POST /users/passwords                        | sso-auth-users-passwords-post         | SSO > Auth > Change Password | pending   |
| /users                                       | POST   | sso-flutter-signup                 | migrated  | 