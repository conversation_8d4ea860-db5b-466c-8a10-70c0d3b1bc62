{"https://api.example.com/integrations/apps/oneapp": {"routes": [{"method": "GET", "path": "/maintenance/bills", "headers": ["Authorization", "Content-Type"], "query_params": [], "body": null}, {"method": "GET", "path": "/maintenance/bills/{billId}", "headers": ["Authorization", "Content-Type"], "query_params": [], "body": null}, {"method": "POST", "path": "/maintenance/payments", "headers": ["Authorization", "Content-Type"], "query_params": [], "body": "{bill_id, amount, paid_by, payment_method, ...}"}, {"method": "POST", "path": "/maintenance/upload", "headers": ["Authorization", "Content-Type"], "query_params": [], "body": "multipart/form-data: cheque_image"}, {"method": "POST", "path": "/auth", "headers": ["Content-Type", "x-oneapp-api-key"], "query_params": [], "body": "{email, phone, firstName?, lastName?, name?}"}, {"method": "GET", "path": "/profile", "headers": ["Authorization", "Content-Type", "x-oneapp-api-key"], "query_params": [], "body": null}, {"method": "GET", "path": "/user/addresses", "headers": ["Authorization", "Content-Type", "x-oneapp-api-key"], "query_params": [], "body": null}, {"method": "POST", "path": "/user/addresses", "headers": ["Authorization", "Content-Type", "x-oneapp-api-key"], "query_params": [], "body": "{address_line_1, address_line_2?, city, state, pincode, is_default}"}, {"method": "PUT", "path": "/user/addresses/{addressId}", "headers": ["Authorization", "Content-Type", "x-oneapp-api-key"], "query_params": [], "body": "{address_line_1, address_line_2?, city, state, pincode}"}, {"method": "PUT", "path": "/user/addresses/{addressId}/set-default", "headers": ["Authorization", "Content-Type", "x-oneapp-api-key"], "query_params": [], "body": null}, {"method": "GET", "path": "/categories", "headers": ["Authorization", "Content-Type", "x-oneapp-api-key"], "query_params": [], "body": null}, {"method": "GET", "path": "/categories/all", "headers": ["Authorization", "Content-Type", "x-oneapp-api-key"], "query_params": [], "body": null}, {"method": "POST", "path": "/service-details", "headers": ["Authorization", "Content-Type", "x-oneapp-api-key"], "query_params": [], "body": "{category_id, subcategory_id, segment_id}"}]}, "https://api.example.com": {"routes": [{"method": "GET", "path": "/api/polls", "headers": ["Content-Type"], "query_params": [], "body": null}, {"method": "POST", "path": "/api/polls/create", "headers": ["Content-Type"], "query_params": [], "body": "{title, description, options, expiresAt, visibility}"}, {"method": "POST", "path": "/api/polls/vote", "headers": ["Content-Type"], "query_params": [], "body": "{pollId, optionId}"}, {"method": "GET", "path": "/api/polls/{pollId}", "headers": ["Content-Type"], "query_params": [], "body": null}]}, "https://gateapi.cubeone.in/api": {"routes": [{"method": "GET", "path": "/{endpoint}", "headers": ["X-Access-Token", "Content-Type", "Accept"], "query_params": [], "body": null}, {"method": "POST", "path": "/{endpoint}", "headers": ["X-Access-Token", "Content-Type", "Accept"], "query_params": [], "body": "{...}"}, {"method": "PUT", "path": "/{endpoint}", "headers": ["X-Access-Token", "Content-Type", "Accept"], "query_params": [], "body": "{...}"}, {"method": "DELETE", "path": "/{endpoint}", "headers": ["X-Access-Token", "Content-Type", "Accept"], "query_params": [], "body": null}]}, "https://maps.googleapis.com": {"routes": [{"method": "GET", "path": "/maps/api/geocode/json", "headers": [], "query_params": ["latlng", "key"], "body": null}]}, "http://authapi.chsone.in/api/v2/": {"routes": [{"method": "POST", "path": "/users/login", "headers": ["api_key", "client_id", "client_secret"], "query_params": [], "body": "{username, password}", "micro_app": "SSO > Auth > Login"}, {"method": "POST", "path": "/users", "headers": ["api_key"], "query_params": [], "body": "{first_name, last_name, ...}", "micro_app": "SSO > Auth > Signup"}, {"method": "GET", "path": "/users/profile", "headers": ["api_key", "access_token"], "query_params": [], "body": null, "micro_app": "SSO > Profile > View"}, {"method": "PUT", "path": "/users/profile", "headers": ["api_key", "access_token"], "query_params": [], "body": "{...}", "micro_app": "SSO > Profile > Update"}, {"method": "POST", "path": "/users/avatars", "headers": ["api_key", "access_token"], "query_params": [], "body": "multipart/form-data: image", "micro_app": "SSO > Profile > Avatar Upload"}, {"method": "GET", "path": "/countries", "headers": ["api_key"], "query_params": [], "body": null, "micro_app": "SSO > Location > Country List"}, {"method": "GET", "path": "/countries/{country_id}/states", "headers": ["api_key"], "query_params": [], "body": null, "micro_app": "SSO > Location > State List"}, {"method": "POST", "path": "/user/addresses", "headers": ["api_key", "access_token"], "query_params": [], "body": "{address, ...}", "micro_app": "SSO > Profile > Address"}, {"method": "POST", "path": "/users/otp", "headers": ["api_key"], "query_params": [], "body": "{mobile, ...}", "micro_app": "SSO > Auth > OTP"}, {"method": "POST", "path": "/users/verifyotp", "headers": ["api_key"], "query_params": [], "body": "{otp, ...}", "micro_app": "SSO > Auth > OTP Verify"}, {"method": "POST", "path": "/users/resetpassword", "headers": ["api_key"], "query_params": [], "body": "{email, ...}", "micro_app": "SSO > Auth > Reset Password"}, {"method": "POST", "path": "/users/passwords", "headers": ["api_key"], "query_params": [], "body": "{password, ...}", "micro_app": "SSO > Auth > Change Password"}]}}